import { lazy } from 'react';
import { Navigate, Route, Routes } from 'react-router-dom';

const ProviderProfile = lazy(() => import('../Provider/ProviderProfile'));
const ServiceDetails = lazy(() => import('./service-review/Service-Details'));
const SearchList = lazy(() => import('./service-list/ServiceList'));

const ServicesRoutes = () => {
  const all_services_routes = [
    {
      path: '/search-list',
      name: 'search-list',
      element: <SearchList />,
      route: Route,
    },
    {
      path: '/service-details/:id/:slug',
      name: 'serviceDetails',
      element: <ServiceDetails />,
      route: Route,
    },
    {
      path: '*',
      name: 'NotFound',
      element: <Navigate to="/" />,
      route: Route,
    },

    // PROVIDER PROFILE ROUTE
    {
      path: '/provider/provider-profile/:id',
      name: 'provider-profile',
      element: <ProviderProfile />,
      route: Route,
    },
  ];
  return (
    <>
      <Routes>
        <Route>
          {all_services_routes.map((route, idx) => (
            <Route path={route.path} element={route.element} key={idx} />
          ))}
        </Route>
      </Routes>
    </>
  );
};

export default ServicesRoutes;

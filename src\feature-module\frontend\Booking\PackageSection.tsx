import { useEffect, useState } from 'react';
import CustomChip from '../../components/CustomChip';
import { TbRosetteDiscountCheckFilled } from 'react-icons/tb';
import { PackageGet, ServiceGet } from '../../../utils/type';

type PackageSectionProps = {
  data: ServiceGet;
  onChange: (packageId: string) => void;
};

const PackageSection = ({ data, onChange }: PackageSectionProps) => {
  const [selectedPackage, setSelectedPackage] = useState<string>('');
  const [packages, setPackages] = useState<PackageGet[]>([]);

  useEffect(() => {
    if (data?.packages) {
      setPackages(data.packages);
    }
  }, [data]);

  // Default select first package
  useEffect(() => {
    if (packages.length > 0) {
      setSelectedPackage(packages[0].packageId);
    }
  }, [packages]);

  // Notify parent
  useEffect(() => {
    if (selectedPackage) {
      onChange(selectedPackage);
    }
  }, [selectedPackage, onChange]);

  return (
    <div className="mt-3 border p-3 rounded-md border-primary/30 bg-gray-50">
      <div className="border-b mb-2 flex justify-between items-center">
        <p className="text-title-bold mb-2">Choose Package</p>
      </div>

      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-3">
        {packages.map((pkg: PackageGet, index: number) => {
          const isSelected = selectedPackage === pkg.packageId;

          return (
            <button
              key={index}
              onClick={() => setSelectedPackage(pkg.packageId)}
              className={`flex flex-row items-center border p-1.5 rounded-md ${
                isSelected
                  ? 'border-primary bg-secondary/10'
                  : 'hover-primary-gradient border-primary/30 bg-white'
              }`}
            >
              <div className="flex flex-col text-left ml-3 w-full">
                <div className="flex justify-between items-center w-full">
                  <p className="text-body">{pkg.packageName || '-'}</p>
                  {pkg.discount?.amount ? (
                    <CustomChip
                      label={`${
                        pkg.discount.valueType !== 'percentage' ? '$' : ''
                      }${pkg.discount.amount}${
                        pkg.discount.valueType === 'percentage' ? '%' : ''
                      } Save`}
                      color="success"
                      size="sm"
                      variant="flat"
                      startContent={
                        <TbRosetteDiscountCheckFilled className="text-sm" />
                      }
                      classNames={{
                        base: 'text-xs',
                        content: 'text-xs',
                      }}
                    />
                  ) : null}
                </div>
                <p className="text-body-bold">${pkg.price ?? '-'}</p>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default PackageSection;

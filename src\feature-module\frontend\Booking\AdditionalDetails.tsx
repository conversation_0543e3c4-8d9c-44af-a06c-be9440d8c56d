import { useState } from 'react';
import { AdditionalGet, ServiceGet } from '../../../utils/type';

type DateAndStaffProps = {
  data: ServiceGet;
  onChange: (services: string[]) => void;
};

const AdditionalDetails = ({ data, onChange }: DateAndStaffProps) => {
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  console.log('Data: ', data);

  const additionalServices = data?.additionalServices || [];

  // ADD / REMOVE ADDITIONAL SERVICE
  const handleSelectAddtionalService = (id: string) => {
    if (!id) return;

    setSelectedServices(
      (prev) =>
        prev.includes(id)
          ? prev.filter((serviceId) => serviceId !== id) // remove if already selected
          : [...prev, id] // add if not selected
    );
  };

  onChange(selectedServices);

  console.log('Selected Additional Services: ', selectedServices);

  return (
    <div className="mt-3 border p-3 rounded-md border-primary/30 bg-gray-50">
      <div className="border-b mb-2 flex justify-between items-center">
        <p className="text-title-bold  mb-2">Choose additional service</p>
      </div>

      {/* CARDS */}
      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-3">
        {additionalServices?.map((a: AdditionalGet, index: number) => {
          const isSelected = selectedServices.includes(a?.id);

          return (
            <button
              key={index}
              onClick={() => handleSelectAddtionalService(a?.id)}
              className={`flex flex-row items-center border p-1.5 rounded-md ${
                isSelected
                  ? 'border-primary bg-secondary/10 '
                  : 'hover-primary-gradient border-primary/30 bg-white'
              }`}
            >
              <span className="w-10 h-10 bg-gray-100 rounded-md overflow-hidden">
                <img
                  src={a?.images}
                  alt="Service"
                  className="w-10 h-10 object-cover"
                />
              </span>

              <div className="flex flex-col text-left ml-3">
                <p className="text-body">{a?.serviceItem || '-'}</p>
                <p className="text-body-bold">${a?.price || '-'}</p>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default AdditionalDetails;

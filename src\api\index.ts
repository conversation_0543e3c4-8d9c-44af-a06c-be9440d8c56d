import axios from 'axios';
import { getToken } from '../tokenprovider';

const age = import.meta.env.VITE_APP_BACKEND_PORT;
console.log('po', age);

export const apiClient = axios.create({
  baseURL: import.meta.env.VITE_APP_BACKEND_PORT,
  timeout: 30000, // Increased to 30 seconds for better reliability
  headers: {
    'Content-Type': 'application/json',
  },
});

apiClient.interceptors.request.use(
  (config) => {
    const token = getToken();
    console.log('Token: ', token);

    if (token) {
      config.headers['Authorization'] = `Bearer ${token} `;
    }
    return config;
  },
  (error) => {
    console.log('axios interceptor error: ', error);
    return Promise.reject(error);
  }
);

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { all_routes } from '../../../core/data/routes/all_routes';
import { AddressSuggestion, Filters, RecentSearchLocation } from '../../../utils/type';
import { Select, SelectItem } from '@heroui/react';
import logo from '../../../assets/logo/1 (1).png';
import { LuMapPin, LuX } from 'react-icons/lu';
import { useCategoryQuery } from '../../../hook/useQueryData';
import { useTranslation } from 'react-i18next';
import axios from 'axios';
import { debounce } from 'lodash';


const SearchBar = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const routes = all_routes;
  const [inputValue, setInputValue] = useState('allcanada');
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [addressInput, setAddressInput] = useState('');
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [, setLocationType] = useState('state');
  const [addressSuggestions, setAddressSuggestions] = useState<AddressSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<AddressSuggestion | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
   const [recentLocations, setRecentLocations] = useState<RecentSearchLocation[]>([]);

  const [filters, setFilters] = useState<Filters>({
    search: '',
    state: 'all',
    categories: [],
    subCategory: '',
    priceRange: [],
    ratings: [],
  });

   useEffect(() => {
    const savedLocations = localStorage.getItem('RecentSearchLocations');
    if (savedLocations) {
      try {
        const locations = JSON.parse(savedLocations);
        setRecentLocations(locations);
        
        // Set the most recent location as default if available
        if (locations.length > 0) {
          const mostRecent = locations[0];
          setInputValue(mostRecent.address_line1 || mostRecent.formatted);
          setSelectedAddress(mostRecent);
          setLatitude(mostRecent.lat);
          setLongitude(mostRecent.lon);
        }
      } catch (error) {
        console.error('Error parsing recent locations:', error);
      }
    }
  }, []);

   const addToRecentLocations = (location: AddressSuggestion) => {
    const newLocation: RecentSearchLocation = {
      ...location,
      timestamp: Date.now()
    };

    const updatedLocations = [
      newLocation,
      ...recentLocations.filter(loc => 
        loc.lat !== location.lat || loc.lon !== location.lon
      ).slice(0, 4) // Keep only 5 most recent, remove duplicates
    ];

    setRecentLocations(updatedLocations);
    localStorage.setItem('RecentSearchLocations', JSON.stringify(updatedLocations));
  };


  useEffect(() => {
    const search = searchParams.get('search');
    const state = searchParams.get('state');
    const category = searchParams.get('categories');
    if (search) {
      setSearchValue(search);
      setFilters((prevFilters) => ({ ...prevFilters, search }));
    }
    if (state) {
      setInputValue(state);
      setFilters((prevFilters) => ({ ...prevFilters, state }));
    }
    if (category) {
      setFilters((prevFilters) => ({
        ...prevFilters,
        categories: category.split(','),
      }));
    }
  }, [searchParams]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setShowModal(false);
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);


  const { data: categoriesData } = useCategoryQuery();

  const setvalues = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    setFilters((prevFilters) => ({ ...prevFilters, search: value }));
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
    setFilters((prevFilters) => ({
      ...prevFilters,
      categories: [e.target.value],
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const searchParamsObj: Record<string, string> = {
        search: filters.search,
        state: filters.state,
        categories: filters.categories.join(',') || 'all',
      };

      if (latitude && longitude) {
        searchParamsObj.latitude = latitude.toString();
        searchParamsObj.longitude = longitude.toString();
      }

      const searchParams = new URLSearchParams(searchParamsObj).toString();
      navigate(`${routes.searchList}?${searchParams}`,{
        state:{
          address: selectedAddress ? selectedAddress.formatted : addressInput,
        }
      });
    } catch (err) {
      console.error('Error fetching search data:', err);
    }
  };

  const { t } = useTranslation();

  const toggleModal = () => {
    setShowModal(!showModal);
    setAddressInput('');
    setAddressSuggestions([]);
    setShowSuggestions(false);
    setSelectedAddress(null);
  };

  const fetchAddressSuggestions = debounce(async (query: string) => {
    if (!query || query.length < 3) {
      setAddressSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await axios.get(
        `https://api.geoapify.com/v1/geocode/autocomplete`,
        {
          params: {
            text: query,
            format: 'json',
            apiKey: import.meta.env.VITE_APP_GEOPIFY_APIKEY,
            countrycodes: 'ca',
            limit: 5
          }
        }
      );

      const suggestions: AddressSuggestion[] = response.data.results.map((result: any) => ({
        formatted: result.formatted,
        address_line1: result.address_line1,
        address_line2: result.address_line2,
        lat: result.lat,
        lon: result.lon
      }));

      setAddressSuggestions(suggestions);
      setShowSuggestions(true);
    } catch (error) {
      console.error('Error fetching address suggestions:', error);
    }
  }, 300);

  const handleAddressInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAddressInput(value);
    fetchAddressSuggestions(value);
  };

  const handleSuggestionClick = (suggestion: AddressSuggestion) => {
    console.log('suggestion', suggestion);
    
    setAddressInput(suggestion.formatted);
    setSelectedAddress(suggestion);
    setShowSuggestions(false);
    setLatitude(suggestion.lat);
    setLongitude(suggestion.lon);
    setLocationType('address');
    addToRecentLocations(suggestion);
  };

  const handleNearMeClick = async () => {
    if (!navigator.geolocation) {
      alert('Geolocation not supported');
      return;
    }

    setIsGettingLocation(true);
    
    navigator.geolocation.getCurrentPosition(
      async (pos) => {
        const lat = pos.coords.latitude;
        const lon = pos.coords.longitude;

        setLatitude(lat);
        setLongitude(lon);
        setLocationType('coordinates');

        try {
          // Use Geoapify for reverse geocoding
          const response = await axios.get(
            `https://api.geoapify.com/v1/geocode/reverse`,
            {
              params: {
                lat: lat,
                lon: lon,
                format: 'json',
                apiKey: import.meta.env.VITE_APP_GEOPIFY_APIKEY
              }
            }
          );

          if (response.data && response.data.results.length > 0) {
            const result = response.data.results[0];
            const formattedAddress = result.formatted;
            const addressLine1 = result.address_line1;
            const addressLine2 = result.address_line2;
            
            setAddressInput(formattedAddress);
            const locationData = {
              formatted: formattedAddress,
              address_line1: addressLine1,
              address_line2: addressLine2,
              lat: lat,
              lon: lon
            };
            setSelectedAddress(locationData);
             addToRecentLocations(locationData);
            
          }
        } catch (error) {
          console.error('Error fetching reverse geocode:', error);
          alert('Failed to fetch address details.');
        } finally {
          setIsGettingLocation(false);
        }
      },
      (err) => {
        console.error('Geolocation error', err.code, err.message);
        alert(`Location error: ${err.message} (code ${err.code})`);
        setIsGettingLocation(false);
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 0 }
    );
  };

  const handleSetLocation = () => {
    if (selectedAddress) {
      const displayText = selectedAddress.address_line1 || selectedAddress.formatted;
      setInputValue(displayText);
      addToRecentLocations(selectedAddress);
      // setFilters((prevFilters) => ({ ...prevFilters, state: displayText }));
      toggleModal();
    } else if (addressInput.trim()) {
      setInputValue(addressInput);
      // setFilters((prevFilters) => ({ ...prevFilters, state: addressInput }));
      toggleModal();
    }
  };

  return (
    <div className="py-3 w-full max-md:flex-wrap max-md:gap-4 max-md:p-4 max-sm:p-2.5 border-b ">
      <div className="px-14 flex justify-between gap-x-20">
        <div className="flex items-center">
          <img
            src={logo}
            alt="Gig Mosaic Logo"
            className="object-contain h-[76px] w-[198px]"
          />
        </div>
        <form
          className="flex gap-3 flex-[2] border rounded-sm items-center px-5"
          onSubmit={handleSubmit}
        >
          <div className="text-base bg-neutral-100 cursor-pointer min-w-[200px] text-zinc-500 ">
            <Select
              aria-label="Select Category"
              className="list-box max-w-sm border-none text-zinc-500 font-primary"
              placeholder={t('search.select_category')}
              variant="bordered"
              radius="none"
              autoFocus={false}
              listboxProps={{
                style: { borderRadius: '0px', borderStartStartRadius: '0px' },
              }}
              classNames={{
                base: '',
                value: '',
              }}
              selectedKeys={[selectedCategory]}
              onChange={handleSelectChange}
            >
              <SelectItem value={''}>{t('search.select_category')}</SelectItem>
              <>
                {categoriesData?.categories?.map((category) => (
                  <SelectItem
                    key={category.categoryId}
                    value={category.categoryId}
                  >
                    {category.categoryName}
                  </SelectItem>
                ))}
              </>
            </Select>
          </div>

          <label htmlFor="searchInput" className="sr-only">
            {t('search.search_products')}
          </label>
          <input
            id="searchInput"
            type="text"
            placeholder={t('search.search_for_servises')}
            value={searchValue}
            onChange={setvalues}
            className="flex-1 p-4 min-h-10 h-10 text-sm rounded-sm bg-neutral-100 border text-zinc-500 max-md:w-full"
          />

          {/* Location Input */}
          <div className="text-base cursor-pointer min-w-[200px] text-zinc-500 relative">
            <button
              type="button"
              onClick={toggleModal}
              className="w-full p-2 flex gap-2 items-center rounded-sm bg-neutral-100 hover:bg-neutral-200"
            >
              <LuMapPin className="w-5 h-5 text-teal-600" /> 
              <span className="truncate">{inputValue}</span>
            </button>
          </div>

          <button
            type="submit"
            aria-label="Search for services"
            className="px-7 min-h-10 h-10 text-base font-semibold text-white bg-teal-500 rounded-sm cursor-pointer border-[none] max-md:w-full"
          >
            {t('search.search_button')}
          </button>
        </form>
      </div>

      {/* Location Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div 
            ref={modalRef}
            className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md"
          >
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Set Location</h2>
              <button
                type="button"
                onClick={toggleModal}
                className="text-gray-500 hover:text-gray-700"
              >
                <LuX className="w-5 h-5" />
              </button>
            </div>
            
            <div className="relative mb-4">
              <input
                ref={inputRef}
                type="text"
                placeholder="Address, city or postal code"
                value={addressInput}
                onChange={handleAddressInputChange}
                className="w-full p-3 rounded-sm border border-gray-300 text-zinc-500"
              />
              {showSuggestions && addressSuggestions.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-sm shadow-lg">
                  {addressSuggestions.map((suggestion, index) => (
                    <div
                      key={index}
                      className="p-3 cursor-pointer hover:bg-gray-100"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      <div className="font-medium">{suggestion.address_line1}</div>
                      {suggestion.address_line2 && (
                        <div className="text-sm text-gray-600">{suggestion.address_line2}</div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="flex flex-col gap-3">
              <button
                type="button"
                onClick={handleNearMeClick}
                disabled={isGettingLocation}
                className="flex items-center justify-center gap-2 px-4 py-2 rounded-sm bg-neutral-100 hover:bg-neutral-200 disabled:opacity-50"
              >
                <LuMapPin className="w-4 h-4" />
                {isGettingLocation ? 'Getting location...' : 'Use current location'}
              </button>
              
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={toggleModal}
                  className="flex-1 px-4 py-2 rounded-sm bg-gray-200 hover:bg-gray-300"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleSetLocation}
                  disabled={!addressInput.trim() && !selectedAddress}
                  className="flex-1 px-4 py-2 rounded-sm bg-teal-500 text-white hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Set location
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchBar;
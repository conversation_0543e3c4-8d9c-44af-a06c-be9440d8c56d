import CustomInput from '../../components/CustomInput';
import { Textarea } from '@heroui/react';

import { useFormContext } from 'react-hook-form';
import CustomButton from '../../components/CustomButton';
import { PersonalGet } from '../../../utils/type';

const AddressSection = () => {
  const {
    register,
    formState: { errors },
  } = useFormContext<PersonalGet>();

  return (
    <div>
      <div className="mt-3 border p-3 rounded-md border-primary/30 bg-gray-50">
        <div className="border-b mb-2 flex justify-between items-center">
          <p className="text-title-bold  mb-2">Address Information</p>
        </div>

        {/* CARDS */}
        <div className="grid xl:grid-cols-3 gap-3 ">
          <div className="xl:col-span-2">
            <div className="grid xl:grid-cols-2 gap-4 mt-3">
              <CustomInput
                type="text"
                isRequired
                label="First Name"
                {...register('firstName')}
                isInvalid={!!errors?.firstName}
                errorMessage={errors?.firstName?.message}
              />
              <CustomInput
                type="text"
                isRequired
                label="Last Name"
                {...register('lastName')}
                isInvalid={!!errors?.lastName}
                errorMessage={errors?.lastName?.message}
              />
              {/* <PhoneInput
                            name="phone"
                            // value={personalInfo.phone}
                            // isInvalid={!!errors.phone}
                            // errorMessage={errors.phone}
                            // onChange={handleChange}
                          /> */}
              <CustomInput
                type="text"
                isRequired
                label="Email"
                {...register('email')}
                isInvalid={!!errors?.email}
                errorMessage={errors?.email?.message}
              />
              <CustomInput
                type="text"
                isRequired
                label="Street Address"
                {...register('address.street')}
                isInvalid={!!errors?.address?.street}
                errorMessage={errors?.address?.street?.message}
              />
              <CustomInput
                type="text"
                isRequired
                label="City"
                {...register('address.city')}
                isInvalid={!!errors?.address?.city}
                errorMessage={errors?.address?.city?.message}
              />
              <CustomInput
                type="text"
                isRequired
                label="State"
                {...register('address.state')}
                isInvalid={!!errors?.address?.state}
                errorMessage={errors?.address?.state?.message}
              />
              <CustomInput
                type="text"
                isRequired
                label="Postal Code"
                {...register('address.postalCode')}
                isInvalid={!!errors?.address?.postalCode}
                errorMessage={errors?.address?.postalCode?.message}
              />
            </div>
            <Textarea
              label="Booking Note"
              labelPlacement="outside"
              variant="bordered"
              className="mt-4"
              classNames={{
                input: 'placeholder:text-gray-400 placeholder:text-xs text-xs',
                label: 'text-xs text-gray-700',
                base: 'text-xs',
                description: 'text-[11px]',
                inputWrapper: 'border border-gray-300 ',
              }}
              {...register('bookingNotes')}
              isInvalid={!!errors?.bookingNotes}
              errorMessage={errors?.bookingNotes?.message}
            />

            <div className="flex justify-end mt-4">
              <CustomButton
                label="Save address"
                variant="solid"
                color="primary"
                type="submit"
              />
            </div>
          </div>

          <div className="border-l flex flex-col gap-3">
            {/* <div className="border border-gray-300 rounded-lg p-4">
                <RadioGroup
                  // label="Select Address"
                  value={selectedAddress}
                  onValueChange={setSelectedAddress}
                >
                  {savedAddresses.map((address, index) => {
                    const info = address.address_line_1 as PersonalInfo;

                    return (
                      <CustomRadio
                        key={address.addressId}
                        value={address?.addressId?.toString() || ''}
                        description={`${info?.streetAddress}, ${info?.city}, ${info?.state} ${info?.postalCode}`}
                        className={index > 0 ? 'mt-4' : ''}
                      >
                        Address {index + 1}
                      </CustomRadio>
                    );
                  })}
                </RadioGroup>
              </div> */}

            <div className="border p-3 rounded-md border-primary/30 ml-2">
              <CustomInput type="text" label="First Name" />
            </div>

            <div className="border p-3 rounded-md border-primary/30 ml-2">
              <CustomInput type="text" label="First Name" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressSection;

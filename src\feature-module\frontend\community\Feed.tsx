// import React, { useState } from 'react';
// import { Card, CardHeader, CardBody } from '@nextui-org/react';
// import PostAddSection from '../../components/PostAddSection/PostAddSection';
// import Post from '../../components/Post/Post';
// import { BsFire } from 'react-icons/bs';
// import ErrorBoundary from '../../utils/Errorboundary';

// function Feed() {
//   return (
//     <div className="mt-10 sm:mt-[80px] font-poppins">
//       <div className="grid md:grid-cols-3  gap-6">
//         <div className="md:col-span-2 gap-6">
//           <ErrorBoundary>
//             <PostAddSection />
//           </ErrorBoundary>

//           <ErrorBoundary>
//             <Post status={false} />
//           </ErrorBoundary>
//         </div>

//         <div className="grid grid-rows-3 gap-6 max-h-[980px]">
//           <Card className="hidden md:block  font-poppins bg-background-secondary-color max-h-2xl">
//             <CardHeader>
//               <div className="flex item-center justify-between gap-2">
//                 <h2 className="text-xl text-gray-200 font-bold font-poppins">
//                   #Top 5 Trending Topics
//                 </h2>
//                 <BsFire size={21} className="text-red-500" />
//               </div>
//             </CardHeader>

//             <CardBody className="gap-4 ml-2">
//               <div className="flex items-center gap-2">
//                 <span className="text-md font-bold text-btn-color">1.</span>
//                 <p className="text-gray-200 text-lg">#Memecoin Trading#</p>
//               </div>
//               {/* <Divider /> */}
//               <div className="flex items-center gap-2">
//                 <span className="text-md font-bold text-btn-color">2.</span>
//                 <p className="text-gray-200 text-lg">#Metawin</p>
//               </div>
//               {/* <Divider /> */}
//               <div className="flex items-center gap-2">
//                 <span className="text-md font-bold text-btn-color">3.</span>
//                 <p className="text-gray-200 text-lg">#US Market</p>
//               </div>
//               {/* <Divider /> */}
//               <div className="flex items-center gap-2 ">
//                 <span className="text-md font-bold text-btn-color">4.</span>
//                 <p className="text-gray-200 text-lg">#BTC Price#</p>
//               </div>
//               {/* <Divider /> */}
//               <div className="flex items-center gap-2 ">
//                 <span className="text-md font-bold text-btn-color">5.</span>
//                 <p className="text-gray-200 text-lg">#Forex Trading#</p>
//               </div>
//             </CardBody>
//           </Card>

//           <Card className="hidden md:block  font-poppins bg-background-secondary-color max-h-2xl">
//             <CardHeader>
//               <p></p>
//             </CardHeader>
//           </Card>

//           <Card className="hidden md:block  font-poppins bg-background-secondary-color max-h-2xl">
//             <CardHeader>
//               <p></p>
//             </CardHeader>
//           </Card>
//         </div>
//       </div>
//     </div>
//   );
// }

// export default Feed;

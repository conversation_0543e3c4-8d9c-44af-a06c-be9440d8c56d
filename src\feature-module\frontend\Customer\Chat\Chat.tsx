import { Chat<PERSON>rovider, Chat as Chat<PERSON>i, initChatConfig, } from '@pubuduth-aplicy/chat-ui';

const Chat = () => {
  initChatConfig({
    apiUrl: `${import.meta.env.VITE_APP_BACKEND_PORT}/chat`,
    // apiUrl: `http://localhost:3030`,
    role: 'customer',
    webSocketUrl: `wss://9085u1qj43.execute-api.ca-central-1.amazonaws.com/prod`
  });
  return (
    <div>
     {/* <ChatProvider userId="660852277c7e0978869f4c15"> */}
     <ChatProvider userId="G_UID_73">
      <ChatUi />
    </ChatProvider>
    </div>
  );
};

export default Chat;

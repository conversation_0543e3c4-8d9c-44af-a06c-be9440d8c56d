import { Spinner, SpinnerProps } from '@heroui/react';

interface ISmallSpinnerProps extends SpinnerProps {
  label?: string;
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning';
  labelColor?:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'foreground';
  size?: 'sm' | 'md' | 'lg';
}

const SmallLoadingSpinner = ({
  label = '',
  color = 'primary',
  size = 'sm',
  labelColor = 'foreground',
}: ISmallSpinnerProps) => {
  return (
    <div className="flex items-center justify-center w-full h-full">
      <Spinner
        label={label}
        color={color}
        size={size}
        labelColor={labelColor}
      />
    </div>
  );
};

export default SmallLoadingSpinner;

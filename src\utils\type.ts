/* eslint-disable @typescript-eslint/no-explicit-any */
// export interface Filters {
//   priceRange?: number | number[];
//   page?: number;
//   search: string;
//   limit?: number;
//   ratingValues?: number | null;
//   state: string;
// }

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export interface FetchSearchDataParams {
  filter: Record<string, any>; // Adjust this type based on your API's expected filter structure
}

export interface FetchSearchDataResponse {
  data: any; // Replace `any` with the actual data structure returned by your API
}

export interface SlideProps {
  image: string;
  title: string;
  description: string;
  button?: string;
}

export interface SubCategoriesResponse {
  success: boolean;
  subCategories: SubCategory[];
  total: number;
  page: number;
  pages: number;
}

export interface CategoriesResponse {
  success: boolean;
  categories: Category[];
  total: number;
  page: number;
  pages: number;
}

export interface CategoriesAndSubResponse {
  success: boolean;
  categoriesWithSubCategories: CategoryWithSub[];
  total: number;
  page: number;
  pages: number;
}

export interface Filters {
  // keyword: string;
  search: string;
  categories: string[];
  subCategory: string | string[];
  // location: string;
  state: string;
  priceRange: number[];
  ratings: number[];
  page?: number;
  latitude?: number | null;
  longitude?: number | null;
  radius?: number | null;
}

export interface SubCategory {
  subCategoryId: string;
  subCategoryName: string;
  subCategorySlug: string;
  categoryName: string;
  categoryId: string;
  createdAt: string;
  isFeatured: boolean;
  isCertificateRequired: boolean;
}

export interface Category {
  categoryId: string;
  categoryName: string;
  categorySlug: string;
  createdAt: string;
  isFeatured: boolean;
  isCertificateRequired: boolean;
}

export interface CategoryWithSub {
  categoryId: string;
  categoryName: string;
  categorySlug: string;
  subCategories: SubCategory[];
  createdAt: string;
  isFeatured: boolean;
  isCertificateRequired: boolean;
}

export interface ServiceData {
  gallery: { serviceImages: string[] }[];
  serviceTitle: string;
  location: {
    city: string;
    state: string;
  }[];
  price: number;
  categoryId: string;
  serviceId: number;
  slug: string;
}

// src/features/booking/types.ts

export interface StaffMember {
  id: string;
  name: string;
  email: string;
  serviceCount: number;
  rating: number;
  image: string;
  services?: string[]; // Array of service IDs this staff can perform
}

export interface Service {
  id: string;
  name: string;
  price: number;
  duration: number;
  rating: number;
  image: string;
  requiresStaff?: boolean; // Whether this service requires staff selection
}

// export interface DateTimeState {
//   selectedDate: string;
//   selectedFromTime: string;
//   selectedToTime: string;
//   timeSlotId: string;
//   selectedTime: string | null;
// }

export interface DateTimeState {
  selectedDate: string;
  selectedFromTime: string;
  selectedToTime: string;
  timeSlotId: string;
  selectedTime: string | null;
  selectedStaff: string;
  selectStaffId: string;
}

export interface StaffState {
  // selectedStaff: StaffMember | null;
  selectedStaff: string;
  selectAnyone: boolean;
  allStaff: StaffMember[];
  availableStaff?: StaffMember[]; // Staff available for selected service
  isStaffAvilableInDay?: boolean; // Flag to check if staff is available on selected date
  isStaffAvilableInService?: boolean; // Flag to check if staff is available on selected time
}

export interface ServicesState {
  // selectedServices: string[];
  // allServices: Service[];
  // additionalServices: AdditionalServiceData[];
  serviceBasicDetails: ServiceBasicDetails[];
}

export interface PersonalInfo {
  addressId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  streetAddress: string;
  city: string;
  state: string;
  postalCode: string;
  notes: string;
  address_line_1?: unknown;
  error: boolean;
}

// export interface BookingState {
//   currentStep: number;
//   dateTime: DateTimeState;
//   staff: StaffState;
//   services: ServicesState;
//   personalInfo: PersonalInfo;
//   selectedService?: Service | null;
// }

export interface AdditionalServiceData {
  id: string;
  price: number;
  serviceItem: number;
  images: string;
}

export interface IPackageProps {
  // isSoldOut: boolean;
  // isDiscount: boolean;
  // packageName: string;
  // price: number;
  // includes?: {
  //   input1?: string;
  //   input2?: string;
  //   input3?: string;
  //   input4?: string;
  // };
  // discount?: {
  //   isDiscount?: boolean;
  //   discountType: 'general-discount';
  //   valueType: 'amount' | 'percentage';
  //   durationType: 'life-time' | 'time-base';
  //   amount: number;
  //   duration: {
  //     start: string;
  //     end?: string;
  //   };
  //   maxCount?: number;
  // };

  isSoldOut: boolean;
  packageId: string;
  serviceId: string;
  providerId: string;
  isDiscount: boolean;
  packageName: string;
  price: number;
  includes?: string[];
  discount: {
    discountId: string;
    serviceId: string;
    serviceName: string;
    packageId: string;
    packageName: string;
    providerId: string;
    isDiscount: boolean;
    discountType: 'general-discount';
    valueType: 'amount' | 'percentage';
    durationType: 'life-time' | 'time-base';
    amount: number;
    duration: {
      start: string;
      end: string;
    };
    maxCount: number;
    createdBy: string;
    isPackageDiscount: boolean;
    acceptAllUserIds: string[];
    createdAt: string;
    updatedAt: string;
  };
}

export interface IDiscountAddProps {
  serviceId: string;
  isDiscount: boolean;
  discountType: 'promo-code' | 'general-discount';
  valueType: 'amount' | 'percentage';
  durationType: 'life-time' | 'time-base';
  amount: number;
  duration: {
    start: string;
    end?: string;
  };
  promoCode?: string;
  maxCount?: number;
}

export interface ServiceBasicDetails {
  serviceId: string;
  providerId: string;
  serviceTitle: string;
  categoryId: string;
  subCategoryId: string;
  price: number;
  isOffers: boolean;
  isAdditional: boolean;
  serviceImages: string;
  city: string;
  state: string;
  country: string;
  staff: string;
  referenceCode?: string;
  availableDate: [];
  packages: IPackageProps[];
  discount: IDiscountAddProps;
}

export type Address = {
  addressLine1?: string;
  country?: string;
  state?: string;
  city?: string;
  postalCode?: string;
};

export type TwoFactor = {
  IsTwoFactorEnabled: boolean;
  lastUpdatedAt?: string | null;
};

export type Password = {
  IsPasswordReset: boolean;
  lastUpdatedAt?: string | null;
};

export type ProviderData = {
  userId: string;
  IsIndividual: boolean;
  IsBusiness: boolean;
  enterpriseId?: string | null;
  name: string;
  email: string;
  mobile?: string;
  dateOfBirth?: string;
  groupRole?: string;
  bio?: string;
  address?: Address;
  currencyCode?: string;
  language?: string;
  profilePicture?: string;
  ISVerified?: boolean;
  providerStatus?: string;
  IsDocUploaded?: boolean;
  IsDocReviewed?: boolean;
  IsDocVerified?: boolean;
  twoFactorEnabled?: TwoFactor;
  password?: Password;
  IsAgreed?: boolean;
  IsActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
};

export interface RecentSearchLocation {
  formatted: string;
  address_line1: string;
  address_line2: string;
  lat: number;
  lon: number;
  timestamp: number;
}

export interface AddressSuggestion {
  formatted: string;
  address_line1: string;
  address_line2: string;
  lat: number;
  lon: number;
}

// PACKAGE
export interface IPackageProps {
  isSoldOut: boolean;
  isDiscount: boolean;
  packageId: string;
  packageName: string;
  price: number;
  // includes?: {
  //   input1?: string;
  //   input2?: string;
  //   input3?: string;
  //   input4?: string;
  // };
  // discount?: {
  //   discountId: string;
  //   isDiscount?: boolean;
  //   discountType: 'general-discount';
  //   valueType: 'amount' | 'percentage';
  //   durationType: 'life-time' | 'time-base';
  //   amount: number;
  //   duration: {
  //     start: string;
  //     end?: string;
  //   };
  //   maxCount?: number;
  // };
}

// BOOKING
export interface ServiceState {
  serviceId: string;
  packageId: string;
  isPackage: boolean;
  date?: string;
  fromTime: string;
  toTime: string;
  timeSlotId: string;
  staffId: string;
  addtionalServiceIds: string[];

  personalInfo: {
    addressId: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    streetAddress: string;
    city: string;
    state: string;
    postalCode: string;
    notes: string;
    error: boolean;
  };
}

export interface BookingState {
  booking: ServiceState;
}

export type PersonalGet = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
  };
  bookingNotes?: string;
};

export interface TimeSlotGet {
  timeSlotId: string;
  from: string;
  to: string;
  availableStaff: [
    {
      staffId: string;
      fullName: string;
      providerStaffId: string;
      isProvider: boolean;
    },
  ];
}

export interface AdditionalGet {
  id: string;
  price: number;
  serviceItem: number;
  images: string;
}

export interface TomeSlotGet {
  from: string;
  to: string;
  maxBookings: number;
  availableSlots: number;
  booked: number;
}
export interface AvailabilityGet {
  id: string;
  day: string;
  timeSlots: TomeSlotGet[];
  alldate: boolean;
}

export interface LocationGet {
  id: string;
  address: string;
  city: string;
  state: string;
  country: 'Canada' | 'canada' | 'ca' | 'CA';
  pinCode: string;
  googleMapsPlaceId?: string;
  latitude?: number;
  longitude?: number;
}

export interface GalleryGet {
  id: string;
  serviceImages: string[];
  serviceVideo?: string[];
  videoLink?: string;
}

export interface SeoGet {
  id: string;
  metaTitle: string;
  metaKeywords: string[];
  metaDescription: string;
}

export interface PackageGet {
  isSoldOut: boolean;
  packageId: string;
  serviceId: string;
  providerId: string;
  isDiscount: boolean;
  packageName: string;
  price: number;
  includes: string[];
  discount: {
    soldOut: boolean;
    discountId: string;
    serviceId: string;
    serviceName: string;
    packageId: string;
    packageName: string;
    providerId: string;
    isDiscount: boolean;
    discountType: 'promo-code' | 'general-discount';
    valueType: 'amount' | 'percentage';
    durationType: 'life-time' | 'time-base';
    amount: number;
    duration: {
      start: string;
      end: string;
    };
    maxCount: number;
    createdBy: string;
    isPackageDiscount: boolean;
    acceptAllUserIds: string[];
    createdAt: string;
    updatedAt: string;
  };
}

export interface DiscountGet {
  discountId: string;
  serviceId: string;
  serviceName: string;
  providerId: string;
  isDiscount: boolean;
  discountType: string;
  valueType: string;
  durationType: string;
  amount: number;
  duration: {
    start: string;
    end: string;
  };
  promoCode: string;
  maxCount: number;
  createdBy: string;
  isPackageDiscount: boolean;
  soldOut: boolean;
  acceptAllUserIds: string[];
  createdAt: string;
  updatedAt: string;
}
export interface ServiceGet {
  serviceId: string;
  providerId: string;
  serviceTitle: string;
  slug: string;
  categoryId: string;
  subCategoryId: string;
  price: number;
  isOffers: boolean;
  // offerPrice: 0;
  // priceAfterDiscount: 0;
  staff: string[];
  includes?: string[];
  serviceOverview: string;
  isActive: boolean;
  isAdditional: boolean;
  isDiscount: boolean;
  isPackage: boolean;
  additionalServices: AdditionalGet[];
  availability: AvailabilityGet[];
  location: LocationGet[];
  gallery: GalleryGet[];
  faq: [];
  seo: SeoGet[];
  discount?: DiscountGet;
  packages?: PackageGet[];
}

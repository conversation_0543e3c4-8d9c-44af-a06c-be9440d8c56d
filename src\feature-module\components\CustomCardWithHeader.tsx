import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, Divider } from '@heroui/react';
import { ReactNode } from 'react';

interface ReusableCardProps {
  title: string;
  subtitle?: string;
  mainContent: ReactNode;
  footerContent?: ReactNode;
  isFooterDevider?: boolean;
  isHeaderDevider?: boolean;
  className?: string;
}

export default function CustomCardWithHeader({
  title,
  subtitle,
  mainContent,
  footerContent,
  isFooterDevider,
  isHeaderDevider,
  className = '',
}: ReusableCardProps) {
  return (
    <Card className={`border border-primary shadow-sm ${className}`}>
      <CardHeader className="flex flex-col items-start">
        <p
          className={`text-md font-semibold text-gray-700 ${!subtitle ? 'mb-2' : ''}  `}
        >
          {title}
        </p>
        {subtitle && <p className="text-xs text-gray-500 mb-2">{subtitle}</p>}
        {isHeaderDevider && <Divider />}
      </CardHeader>
      <CardBody>{mainContent}</CardBody>
      {footerContent && (
        <CardFooter>
          {isFooterDevider && <Divider />}
          {footerContent}
        </CardFooter>
      )}
    </Card>
  );
}

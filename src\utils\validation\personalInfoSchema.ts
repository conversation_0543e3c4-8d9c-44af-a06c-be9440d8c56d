import * as yup from 'yup';

export const personalInfoSchema = yup.object().shape({
  firstName: yup
    .string()
    .required('First name is required')
    .min(2, 'First name must be at least 2 characters')
    .max(50, 'First name must be at most 50 characters')
    .matches(
      /^[a-zA-Z\s]*$/,
      'First name must contain only letters and spaces'
    ),

  lastName: yup
    .string()
    .required('Last name is required')
    .min(2, 'Last name must be at least 2 characters')
    .max(50, 'Last name must be at most 50 characters')
    .matches(/^[a-zA-Z\s]*$/, 'Last name must contain only letters and spaces'),

  email: yup.string().email('Invalid email').required('Email is required'),

  phone: yup
    .string()
    .required('Phone Number is required')
    // .matches(
    //   /^(?:\+1[-\s]?)?\(?\d{3}\)?[-\s]?\d{3}[-\s]?\d{4}$/,
    //   "Phone Number is not valid"
    // );
    .matches(
      /^\+[0-9]{11,15}$/,
      'Phone Number is not valid (format: +1XXXXXXXXXX)'
    ),

  address: yup.object().shape({
    street: yup
      .string()
      .required('Street  is required')
      .min(2, 'Street name must be at least 2 characters')
      .max(50, 'Street name must be at most 50 characters'),
    city: yup
      .string()
      .required('City is required')
      .min(2, 'City must be at least 2 characters')
      .max(20, 'City must be at most 20 characters')
      .matches(/^[a-zA-Z\s]*$/, 'City must contain only letters and spaces'),
    state: yup
      .string()
      .required('State address is required')
      .min(2, 'State name must be at least 2 characters')
      .max(20, 'State name must be at most 20 characters')
      .matches(
        /^[a-zA-Z\s]*$/,
        'State name must contain only letters and spaces'
      ),
    // Inside personalInfoSchema...
    postalCode: yup
      .string()
      .required('Pincode is required')
      .matches(
        /^([ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ])\s*([0-9][ABCEGHJKLMNPRSTVWXYZ][0-9])$/i,
        'Pincode must be valid'
      ),
  }),

  bookingNotes: yup
    .string()
    .max(250, 'Notes must be at most 250 characters')
    .optional()
    .nullable(),
});

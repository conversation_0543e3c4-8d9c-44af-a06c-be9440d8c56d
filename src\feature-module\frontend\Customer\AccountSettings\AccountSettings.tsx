import { Card } from "@heroui/react";
import { Tabs, Tab } from "@heroui/react";
import { Button, Input, Textarea, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from "@heroui/react";
import toast, { Toaster } from 'react-hot-toast';

import { useAuth } from 'react-oidc-context';
import { useEffect, useState, useCallback, useRef } from 'react';
import { apiClient } from '../../../../api';
import { uploadToS3, uploadProfilePictureToS3, deleteProfilePictureFromS3 } from '../aws/s3FileUpload';
import { ROLE } from '../../../../Role';
import moment from 'moment';
import {
  validateField,
  validateUserData,
  sanitizePhoneNumber,
  type UserDataForValidation
} from './account-validation';
import SecuritySettings from '../SecuritySettings/SecuritySettings';

// Define user data interface
interface UserData {
  name?: string;
  username?: string;
  email?: string;
  phone?: string;
  mobile?: string;
  gender?: string;
  dateOfBirth?: string;
  bio?: string;
  address?: {
    addressLine1?: string;
    addressLine2?: string;
    country?: string;
    state?: string;
    city?: string;
    postalCode?: string;
  };
  currencyCode?: string;
  language?: string;
  profilePicture?: string;
}

const AccountSettings = () => {
  const auth = useAuth();
  const [userData, setUserData] = useState<UserData>({});
  const [loading, setLoading] = useState(true);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [removeLoading, setRemoveLoading] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editFormData, setEditFormData] = useState<UserData>({});
  const [editFormErrors, setEditFormErrors] = useState<Record<string, string>>({});
  const [editSaveLoading, setEditSaveLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const editFormFileInputRef = useRef<HTMLInputElement>(null);

  // Role-based address editing states
  const [isProvider, setIsProvider] = useState(false);

  // Get user ID from auth context
  const preferredUsername = auth.user?.profile?.preferred_username;
  const sub = auth.user?.profile?.sub;
  const email = auth.user?.profile?.email;
  const uid = preferredUsername || sub || email;

  // Check user role and set appropriate state
  useEffect(() => {
    if (auth.user) {
      const roles = auth.user?.profile['cognito:groups'] as string[] | undefined;
      const hasProviderRole = roles?.includes(ROLE.PROVIDER);
      setIsProvider(hasProviderRole || false);
    }
  }, [auth.user]);

  // Function to fetch user data
  const fetchUserData = useCallback(async () => {
    if (!uid) {
      setLoading(false);
      toast.error("User ID not found. Please log in again.");
      return;
    }

    try {
      setLoading(true);

      if (!auth.isAuthenticated) {
        toast.error("You are not logged in. Please log in to view your account settings.");
        setLoading(false);
        return;
      }

      const response = await apiClient.get(`http://localhost:3010/api/v1/user/${uid}`);

      if (response.status === 200 && response.data) {
        const apiData = response.data.user || response.data;
        const adaptedData: UserData = {
          name: apiData.name || apiData.fullName || apiData.displayName || '',
          email: apiData.email || '',
          mobile: apiData.mobile || apiData.phone || apiData.phoneNumber || '',
          gender: apiData.gender || apiData.sex || '',
          dateOfBirth: apiData.dateOfBirth || apiData.dob || '',
          bio: apiData.bio || apiData.description || '',
          language: apiData.language || apiData.preferredLanguage || '',
          currencyCode: apiData.currencyCode || apiData.currency || '',
          profilePicture: apiData.profilePicture || apiData.profileImage || apiData.avatar || apiData.picture || '',
          address: apiData.address ? {
            addressLine1: apiData.address.addressLine1 || apiData.address.street || apiData.address.line1 || '',
            addressLine2: apiData.address.addressLine2 || apiData.address.line2 || '',
            country: apiData.address.country || '',
            state: apiData.address.state || apiData.address.province || apiData.address.region || '',
            city: apiData.address.city || '',
            postalCode: apiData.address.postalCode || apiData.address.zipCode || apiData.address.zip || ''
          } : {
            addressLine1: '',
            addressLine2: '',
            country: '',
            state: '',
            city: '',
            postalCode: ''
          }
        };

        // Fallback data for testing if no data found
        if (!adaptedData.name && !adaptedData.email) {
          adaptedData.name = auth.user?.profile?.name || auth.user?.profile?.given_name || 'Test User';
          adaptedData.email = auth.user?.profile?.email || '<EMAIL>';
          adaptedData.mobile = '+1234567890';
          adaptedData.gender = 'prefer-not-to-say';
          adaptedData.bio = 'This is a sample bio for testing purposes.';
          adaptedData.currencyCode = 'USD';
          adaptedData.language = 'English';
        }

        setUserData(adaptedData);
        toast.success(" ");
      } else {
        toast.error("Failed to fetch user data. Please try again later.");
      }
    } catch (err: unknown) {
      console.error('Error fetching user data:', err);
      toast.error("An error occurred while fetching your account data.");
    } finally {
      setLoading(false);
    }
  }, [uid, auth.isAuthenticated, auth.user?.profile?.name, auth.user?.profile?.given_name, auth.user?.profile?.email]);

  // Call fetchUserData when component mounts
  useEffect(() => {
    if (uid) {
      fetchUserData();
    }
  }, [uid, fetchUserData]);

  // Edit form functions
  const handleOpenEditForm = () => {
    setEditFormData({ ...userData });
    setEditFormErrors({});
    setShowEditForm(true);
  };

  const handleCloseEditForm = () => {
    setShowEditForm(false);
    setEditFormData({});
    setEditFormErrors({});
    setEditSaveLoading(false);
  };

  const handleEditFormChange = (field: string, value: string | number) => {
    if (field === 'email') return; // Disallow editing email

    setEditFormData((prevData) => {
      const updatedData = { ...prevData };
      const keys = field.split('.');
      let current: Record<string, unknown> = updatedData as Record<string, unknown>;

      for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!current[key]) current[key] = {};
        current = current[key] as Record<string, unknown>;
      }

      const lastKey = keys[keys.length - 1];
      if (lastKey === 'mobile' || lastKey === 'phone') {
        current[lastKey] = sanitizePhoneNumber(String(value));
      } else {
        current[lastKey] = value;
      }

      return updatedData;
    });

    // Real-time validation
    setTimeout(() => {
      const error = validateField(field, value);
      setEditFormErrors(prev => {
        const newErrors = { ...prev };
        if (error) {
          newErrors[field] = error;
        } else {
          delete newErrors[field];
        }
        return newErrors;
      });
    }, 300);
  };

  // Helper function to save user data to backend
  const saveUserData = async (dataToSave: UserData) => {
    if (!uid) {
      throw new Error("User ID not found. Please log in again.");
    }

    if (!auth.isAuthenticated) {
      throw new Error("You are not logged in. Please log in to save your account settings.");
    }

    const token = auth.user?.id_token;
    if (!token) {
      throw new Error("Authentication token not found. Please log in again.");
    }

    try {
      if (!dataToSave.name || !dataToSave.email) {
        throw new Error('Name and email are required fields');
      }

      const formattedData = {
        ...dataToSave,
        dateOfBirth: dataToSave.dateOfBirth && moment(dataToSave.dateOfBirth).isValid()
          ? moment(dataToSave.dateOfBirth).format('YYYY-MM-DD')
          : dataToSave.dateOfBirth || null,
        profilePicture: dataToSave.profilePicture || '',
        name: dataToSave.name || '',
        email: dataToSave.email || '',
        mobile: dataToSave.mobile || '',
        phone: dataToSave.phone || '',
        gender: dataToSave.gender || '',
        bio: dataToSave.bio || '',
        language: dataToSave.language || '',
        currencyCode: dataToSave.currencyCode || '',
        address: dataToSave.address ? {
          addressLine1: dataToSave.address.addressLine1 || '',
          addressLine2: dataToSave.address.addressLine2 || '',
          country: dataToSave.address.country || '',
          state: dataToSave.address.state || '',
          city: dataToSave.address.city || '',
          postalCode: dataToSave.address.postalCode || ''
        } : {}
      };

      const response = await apiClient.put(`/api/v1/user/${uid}`, formattedData, {
        timeout: 45000,
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.status !== 200) {
        throw new Error(`Failed to update user data. Server returned status ${response.status}`);
      }

      if (!response.data) {
        throw new Error("Failed to update user data. No data returned from server.");
      }

      const data = response.data as unknown;
      const normalized: UserData = (typeof data === 'object' && data !== null && 'user' in (data as Record<string, unknown>))
        ? (data as { user: UserData }).user
        : (data as UserData);
      return normalized;
    } catch (error: unknown) {
      console.error('Error in saveUserData:', error);
      if (error instanceof Error) {
        throw new Error(`Failed to save user data: ${error.message}`);
      } else {
        throw new Error("Failed to save user data due to an unknown error");
      }
    }
  };

  const validateEditForm = () => {
    const userDataForValidation: UserDataForValidation = {
      name: editFormData.name,
      email: editFormData.email,
      mobile: editFormData.mobile,
      phone: editFormData.phone,
      gender: editFormData.gender,
      dateOfBirth: editFormData.dateOfBirth,
      bio: editFormData.bio,
      address: editFormData.address,
      currencyCode: editFormData.currencyCode,
      language: editFormData.language
    };

    const validationResult = validateUserData(userDataForValidation, isProvider);
    const formErrors: Record<string, string> = {};
    validationResult.errors.forEach(error => {
      formErrors[error.field] = error.message;
    });

    setEditFormErrors(formErrors);
    return validationResult.isValid;
  };

  const handleSaveEditForm = async () => {
    if (!validateEditForm()) {
      return;
    }

    try {
      setEditSaveLoading(true);
      await saveUserData(editFormData);
      await fetchUserData();
      toast.success("Your account information has been updated successfully!");
      handleCloseEditForm();
    } catch (err: unknown) {
      console.error('Error updating user data:', err);
      toast.error(err instanceof Error ? err.message : "An error occurred while updating your account data.");
    } finally {
      setEditSaveLoading(false);
    }
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    if (!validTypes.includes(file.type)) {
      toast.error("Invalid file type. Please upload a .jpg or .png file.");
      return;
    }

    if (file.size > 512000) {
      toast.error("File is too large. Maximum size is 500KB.");
      return;
    }

    try {
      setUploadLoading(true);
      const imageUrl = await uploadToS3(file, 'profile-images');

      if (!imageUrl) {
        throw new Error("Failed to get image URL after upload");
      }

      const updatedUserData = {
        ...userData,
        profilePicture: imageUrl
      };

      setUserData(updatedUserData);
      await saveUserData(updatedUserData);
      await fetchUserData();
      toast.success("Profile picture uploaded and saved successfully!");

      window.dispatchEvent(new CustomEvent('profilePictureUpdated', {
        detail: {
          profileImage: imageUrl,
          userId: uid
        }
      }));

    } catch (err: unknown) {
      console.error("Error uploading profile picture:", err);
      toast.error("Failed to upload profile picture. Please try again.");
    } finally {
      setUploadLoading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  // Handle profile picture upload in edit form
  const handleEditFormProfileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error("Invalid file type. Please upload a .jpg, .png, or .webp file.");
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error("File is too large. Maximum size is 5MB.");
      return;
    }

    try {
      setUploadLoading(true);

      if (!uid) {
        throw new Error("User ID not found");
      }

      const uploadResult = await uploadProfilePictureToS3(file, uid);

      if (!uploadResult || !uploadResult.fullUrl) {
        throw new Error("Failed to get image URL after upload");
      }

      // Update edit form data with new profile picture
      setEditFormData(prev => ({
        ...prev,
        profilePicture: uploadResult.fullUrl
      }));

      toast.success("Profile picture uploaded successfully!");

    } catch (err: unknown) {
      console.error("Error uploading profile picture:", err);
      toast.error("Failed to upload profile picture. Please try again.");
    } finally {
      setUploadLoading(false);
      if (editFormFileInputRef.current) {
        editFormFileInputRef.current.value = '';
      }
    }
  };

  // Handle profile picture removal in edit form
  const handleEditFormProfileRemove = async () => {
    try {
      setRemoveLoading(true);

      // Extract image name from current profile picture URL if it exists
      const currentProfilePicture = editFormData.profilePicture;
      if (currentProfilePicture) {
        try {
          // Extract image name from URL for deletion
          const urlParts = currentProfilePicture.split('/');
          const imageName = urlParts[urlParts.length - 1];

          if (imageName && imageName !== 'placeholder') {
            await deleteProfilePictureFromS3(imageName);
          }
        } catch (deleteError) {
          console.warn("Could not delete old profile picture from S3:", deleteError);
          // Continue with removal even if S3 deletion fails
        }
      }

      // Update edit form data to remove profile picture
      setEditFormData(prev => ({
        ...prev,
        profilePicture: ''
      }));

      toast.success("Profile picture removed successfully!");

    } catch (err: unknown) {
      console.error("Error removing profile picture:", err);
      toast.error("Failed to remove profile picture. Please try again.");
    } finally {
      setRemoveLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="p-6 min-h-screen flex items-center justify-center">
        <div className="text-center">Loading account data...</div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#fff',
            color: '#333',
            border: '1px solid #e5e7eb',
            borderRadius: '8px',
            fontSize: '14px',
          },
          success: {
            iconTheme: {
              primary: '#10b981',
              secondary: '#fff',
            },
          },
          error: {
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />

      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <img
            src={userData.profilePicture || "https://via.placeholder.com/80"}
            alt="Profile"
            className="w-20 h-20 rounded-full object-cover"
          />
          <div>
            <h2 className="text-2xl font-semibold">{userData.name || "User Name"}</h2>
            <span className="text-gray-500">{isProvider ? "Business Pro" : ""}</span>
          </div>
        </div>
        <Button
          color="primary"
          radius="none"
          onPress={handleOpenEditForm}
        >
          Edit
        </Button>
      </div>

      {/* Tabs */}
      <Tabs aria-label="Profile Tabs" variant="underlined" className="mb-6">
        <Tab key="general" title="General">

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Left Side - General Information */}
            <div className="space-y-6">
              {/* General Information */}
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">General Information</h3>
                <div className="space-y-4">
                  <Input
                    label="Full Name"
                    placeholder="Enter your full name"
                    value={userData.name || ''}
                    isReadOnly
                  />
                  <Input
                    label="Email Address"
                    placeholder="Enter your email"
                    value={userData.email || ''}
                    isReadOnly
                  />
                  <Input
                    label="Phone Number"
                    placeholder="+1 xxx xxxx xxx"
                    value={userData.mobile || ''}
                    isReadOnly
                  />
                  <Select
                    label="Gender"
                    placeholder="Select your gender"
                    selectedKeys={userData.gender ? [userData.gender] : []}
                    isDisabled
                  >
                    <SelectItem key="male">Male</SelectItem>
                    <SelectItem key="female">Female</SelectItem>
                    <SelectItem key="other">Other</SelectItem>
                  </Select>
                  <Input
                    type="date"
                    label="Date of Birth"
                    description="You must be at least 13 years old"
                    value={userData.dateOfBirth || ''}
                    isReadOnly
                  />
                  <Textarea
                    label="Bio"
                    placeholder="Tell us about yourself"
                    maxLength={500}
                    description={`${userData.bio?.length || 0}/500 characters`}
                    value={userData.bio || ''}
                    isReadOnly
                  />
                </div>
              </Card>

              {/* Preferences */}
              {/* <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Preferences</h3>
                <div className="space-y-4">
                  <Select
                    label="Currency"
                    placeholder="Choose your currency"
                    selectedKeys={userData.currencyCode ? [userData.currencyCode] : []}
                    isDisabled
                  >
                    <SelectItem key="USD">USD</SelectItem>
                    <SelectItem key="CAD">CAD</SelectItem>
                    <SelectItem key="EUR">EUR</SelectItem>
                    <SelectItem key="LKR">LKR</SelectItem>
                  </Select>
                  <Select
                    label="Language"
                    placeholder="Select language"
                    selectedKeys={userData.language ? [userData.language] : []}
                    isDisabled
                  >
                    <SelectItem key="English">English</SelectItem>
                    <SelectItem key="French">French</SelectItem>
                    <SelectItem key="German">German</SelectItem>
                    <SelectItem key="Sinhala">Sinhala</SelectItem>
                  </Select>
                </div>
              </Card> */}
            </div>

            {/* Right Side - Address Information */}
            <div className="space-y-6">
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">Address Information</h3>
                <div className="space-y-4">
                  <Input
                    label="Address Line 1"
                    placeholder="Enter address line 1"
                    value={userData.address?.addressLine1 || ''}
                    isReadOnly
                  />
                  <Input
                    label="Address Line 2"
                    placeholder="Enter address line 2 (optional)"
                    value={userData.address?.addressLine2 || ''}
                    isReadOnly
                  />
                  <Input
                    label="Country"
                    placeholder="Enter your country"
                    value={userData.address?.country || ''}
                    isReadOnly
                  />
                  <Input
                    label="State/Province"
                    placeholder="Enter your state"
                    value={userData.address?.state || ''}
                    isReadOnly
                  />
                  <Input
                    label="City"
                    placeholder="Enter your city"
                    value={userData.address?.city || ''}
                    isReadOnly
                  />
                  <Input
                    label="Postal Code"
                    placeholder="Enter postal code"
                    value={userData.address?.postalCode || ''}
                    isReadOnly
                  />

                <div className="space-y-4">
                  {/* <Select
                    label="Currency"
                    placeholder="Choose your currency"
                    selectedKeys={userData.currencyCode ? [userData.currencyCode] : []}
                    isDisabled
                  >
                    <SelectItem key="USD">USD</SelectItem>
                    <SelectItem key="CAD">CAD</SelectItem>
                    <SelectItem key="EUR">EUR</SelectItem>
                    <SelectItem key="LKR">LKR</SelectItem>
                  </Select> */}
                  <Select
                    label="Language"
                    placeholder="Select language"
                    selectedKeys={userData.language ? [userData.language] : []}
                    isDisabled
                  >
                    <SelectItem key="English">English</SelectItem>
                    <SelectItem key="French">French</SelectItem>
                    <SelectItem key="German">German</SelectItem>

                  </Select>
                </div>
                </div>
              </Card>
            </div>
          </div>
        </Tab>
        <Tab key="security" title="Security">
          <SecuritySettings />
        </Tab>
      </Tabs>

      {/* Hidden file inputs for profile picture upload */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept=".jpg,.jpeg,.png"
        onChange={handleFileUpload}
      />
      <input
        type="file"
        ref={editFormFileInputRef}
        className="hidden"
        accept=".jpg,.jpeg,.png,.webp"
        onChange={handleEditFormProfileUpload}
      />

      {/* Edit Form Modal */}
      <Modal
        isOpen={showEditForm}
        onClose={handleCloseEditForm}
        size="5xl"
        scrollBehavior="inside"
        isDismissable={!editSaveLoading}
        isKeyboardDismissDisabled={editSaveLoading}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-xl font-bold text-gray-800">Edit Account Information</h2>
            <p className="text-sm text-gray-600 font-normal">Update your personal information and preferences</p>
          </ModalHeader>
          <ModalBody>
            {/* Profile Picture Section */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-700 mb-4">Profile Picture</h3>
              <div className="flex items-center gap-4">
                <img
                  src={editFormData.profilePicture || "https://via.placeholder.com/80"}
                  alt="Profile"
                  className="w-20 h-20 rounded-full object-cover border-2 border-gray-200"
                />
                <div className="flex gap-2">
                  <Button
                    color="primary"
                    variant="flat"
                    size="sm"
                    onPress={() => editFormFileInputRef.current?.click()}
                    isLoading={uploadLoading}
                    isDisabled={uploadLoading || removeLoading}
                  >
                    {uploadLoading ? 'Uploading...' : 'Upload New'}
                  </Button>
                  {editFormData.profilePicture && (
                    <Button
                      color="danger"
                      variant="flat"
                      size="sm"
                      onPress={handleEditFormProfileRemove}
                      isLoading={removeLoading}
                      isDisabled={uploadLoading || removeLoading}
                    >
                      {removeLoading ? 'Removing...' : 'Remove'}
                    </Button>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-2">
                Supported formats: JPG, PNG, WebP. Maximum size: 5MB.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Left Side - General Information and Preferences */}
              <div className="space-y-6">
                {/* General Information Section */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">General Information</h3>
                  <div className="space-y-4">
                    <Input
                      label="Full Name"
                      isRequired
                      placeholder="Enter your full name"
                      value={editFormData.name || ''}
                      onValueChange={(value) => handleEditFormChange('name', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors.name}
                      errorMessage={editFormErrors.name}
                    />
                    <Input
                      label="Email Address"
                      isRequired
                      placeholder="Enter your email"
                      value={editFormData.email || ''}
                      variant="bordered"
                      isDisabled
                      description="Email cannot be edited"
                    />
                    <Input
                      label="Phone Number"
                      placeholder="+1 xxx xxxx xxx"
                      value={editFormData.mobile || ''}
                      onValueChange={(value) => handleEditFormChange('mobile', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors.mobile}
                      errorMessage={editFormErrors.mobile}
                    />
                    <Select
                      label="Gender"
                      placeholder="Select your gender"
                      selectedKeys={editFormData.gender ? [editFormData.gender] : []}
                      onSelectionChange={(keys) => {
                        const selectedKey = Array.from(keys)[0] as string;
                        handleEditFormChange('gender', selectedKey);
                      }}
                      variant="bordered"
                    >
                      <SelectItem key="male">Male</SelectItem>
                      <SelectItem key="female">Female</SelectItem>
                      <SelectItem key="other">Other</SelectItem>
                    </Select>
                    <Input
                      type="date"
                      label="Date of Birth"
                      description="You must be at least 13 years old"
                      value={editFormData.dateOfBirth || ''}
                      onValueChange={(value) => handleEditFormChange('dateOfBirth', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors.dateOfBirth}
                      errorMessage={editFormErrors.dateOfBirth}
                    />
                    <Textarea
                      label="Bio"
                      placeholder="Tell us about yourself"
                      maxLength={500}
                      description={`${editFormData.bio?.length || 0}/500 characters`}
                      value={editFormData.bio || ''}
                      onValueChange={(value) => handleEditFormChange('bio', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors.bio}
                      errorMessage={editFormErrors.bio}
                    />
                  </div>
                </div>

                {/* Preferences Section */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">Preferences</h3>
                  <div className="space-y-4">
                    <Select
                      label="Currency"
                      placeholder="Choose your currency"
                      selectedKeys={editFormData.currencyCode ? [editFormData.currencyCode] : []}
                      onSelectionChange={(keys) => {
                        const selectedKey = Array.from(keys)[0] as string;
                        handleEditFormChange('currencyCode', selectedKey);
                      }}
                      variant="bordered"
                    >
                      <SelectItem key="USD">USD</SelectItem>
                      <SelectItem key="CAD">CAD</SelectItem>
                      <SelectItem key="EUR">EUR</SelectItem>
                      <SelectItem key="LKR">LKR</SelectItem>
                    </Select>
                    <Select
                      label="Language"
                      placeholder="Select language"
                      selectedKeys={editFormData.language ? [editFormData.language] : []}
                      onSelectionChange={(keys) => {
                        const selectedKey = Array.from(keys)[0] as string;
                        handleEditFormChange('language', selectedKey);
                      }}
                      variant="bordered"
                    >
                      <SelectItem key="English">English</SelectItem>
                      <SelectItem key="French">French</SelectItem>
                      <SelectItem key="German">German</SelectItem>
                      <SelectItem key="Sinhala">Sinhala</SelectItem>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Right Side - Address Information */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-700 mb-4">Address Information</h3>
                  <div className="space-y-4">
                    <Input
                      label="Address Line 1"
                      placeholder="Enter address line 1"
                      value={editFormData.address?.addressLine1 || ''}
                      onValueChange={(value) => handleEditFormChange('address.addressLine1', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors['address.addressLine1']}
                      errorMessage={editFormErrors['address.addressLine1']}
                    />
                    <Input
                      label="Address Line 2"
                      placeholder="Enter address line 2 (optional)"
                      value={editFormData.address?.addressLine2 || ''}
                      onValueChange={(value) => handleEditFormChange('address.addressLine2', value)}
                      variant="bordered"
                    />
                    <Input
                      label="Country"
                      placeholder="Enter your country"
                      value={editFormData.address?.country || ''}
                      onValueChange={(value) => handleEditFormChange('address.country', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors['address.country']}
                      errorMessage={editFormErrors['address.country']}
                    />
                    <Input
                      label="State/Province"
                      placeholder="Enter your state"
                      value={editFormData.address?.state || ''}
                      onValueChange={(value) => handleEditFormChange('address.state', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors['address.state']}
                      errorMessage={editFormErrors['address.state']}
                    />
                    <Input
                      label="City"
                      placeholder="Enter your city"
                      value={editFormData.address?.city || ''}
                      onValueChange={(value) => handleEditFormChange('address.city', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors['address.city']}
                      errorMessage={editFormErrors['address.city']}
                    />
                    <Input
                      label="Postal Code"
                      placeholder="Enter postal code"
                      value={editFormData.address?.postalCode || ''}
                      onValueChange={(value) => handleEditFormChange('address.postalCode', value)}
                      variant="bordered"
                      isInvalid={!!editFormErrors['address.postalCode']}
                      errorMessage={editFormErrors['address.postalCode']}
                    />

                  </div>
                </div>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" radius="none" onPress={handleCloseEditForm}>
              Cancel
            </Button>
            <Button
              color="primary"
              radius="none"
              onPress={handleSaveEditForm}
              isLoading={editSaveLoading}
              isDisabled={editSaveLoading}
            >
              {editSaveLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default AccountSettings;

import React, { useState, useRef } from 'react';
import { Button, Progress, Modal, Modal<PERSON>ontent, ModalHeader, <PERSON>dal<PERSON>ody, ModalFooter } from '@heroui/react';
import { FiUpload, FiCamera, FiTrash2 } from 'react-icons/fi';
import { toast } from 'react-toastify';
import ProfilePictureDisplay from './ProfilePictureDisplay';
import { 
  uploadAndSaveProfilePicture, 
  deleteProfilePicture, 
  validateProfilePictureFile,
  ProfilePictureUploadResult 
} from '../../../service/profilePictureService';

interface ProfilePictureUploadProps {
  userId: string;
  currentProfileImage?: string;
  userName?: string;
  onUploadSuccess?: (result: ProfilePictureUploadResult) => void;
  onDeleteSuccess?: () => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showUploadButton?: boolean;
  showDeleteButton?: boolean;
}

const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({
  userId,
  currentProfileImage,
  userName,
  onUploadSuccess,
  onDeleteSuccess,
  className = '',
  size = 'lg',
  showUploadButton = true,
  showDeleteButton = true
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = validateProfilePictureFile(file);
    if (!validation.isValid) {
      toast.error(validation.error);
      return;
    }

    await handleUpload(file);
  };

  const handleUpload = async (file: File) => {
    try {
      setIsUploading(true);
      setUploadProgress(10);

      // Create progress simulation
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      console.log('Starting profile picture upload...', {
        fileName: file.name,
        fileSize: file.size,
        userId
      });

      const result = await uploadAndSaveProfilePicture(file, userId, currentProfileImage);

      clearInterval(progressInterval);
      setUploadProgress(100);

      toast.success('Profile picture updated successfully!');
      
      if (onUploadSuccess) {
        onUploadSuccess(result);
      }

      // Reset progress after a short delay
      setTimeout(() => {
        setUploadProgress(0);
      }, 1000);

    } catch (error) {
      console.error('Profile picture upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload profile picture';
      toast.error(errorMessage);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDelete = async () => {
    if (!currentProfileImage) {
      toast.error('No profile picture to delete');
      return;
    }

    try {
      setIsDeleting(true);
      
      await deleteProfilePicture(userId, currentProfileImage);
      
      toast.success('Profile picture deleted successfully!');
      
      if (onDeleteSuccess) {
        onDeleteSuccess();
      }
      
      setShowDeleteModal(false);
    } catch (error) {
      console.error('Profile picture deletion error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete profile picture';
      toast.error(errorMessage);
    } finally {
      setIsDeleting(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* Profile Picture Display */}
      <div className="relative">
        <ProfilePictureDisplay
          profileImage={currentProfileImage}
          userName={userName}
          size={size}
          showBorder={true}
          isClickable={showUploadButton}
          onClick={showUploadButton ? triggerFileInput : undefined}
        />
        
        {/* Upload overlay on hover */}
        {showUploadButton && (
          <div 
            className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer"
            onClick={triggerFileInput}
          >
            <FiCamera className="text-white text-xl" />
          </div>
        )}
      </div>

      {/* Upload Progress */}
      {isUploading && (
        <div className="w-full max-w-xs">
          <Progress 
            value={uploadProgress} 
            className="w-full"
            color="primary"
            size="sm"
          />
          <p className="text-sm text-gray-600 mt-1 text-center">
            Uploading... {uploadProgress}%
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex space-x-2">
        {showUploadButton && (
          <Button
            color="primary"
            variant="flat"
            startContent={<FiUpload />}
            onClick={triggerFileInput}
            isDisabled={isUploading}
            size="sm"
          >
            {currentProfileImage ? 'Change Picture' : 'Upload Picture'}
          </Button>
        )}

        {showDeleteButton && currentProfileImage && (
          <Button
            color="danger"
            variant="flat"
            startContent={<FiTrash2 />}
            onClick={() => setShowDeleteModal(true)}
            isDisabled={isUploading || isDeleting}
            size="sm"
          >
            Delete
          </Button>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Delete Confirmation Modal */}
      <Modal 
        isOpen={showDeleteModal} 
        onClose={() => setShowDeleteModal(false)}
        size="sm"
      >
        <ModalContent>
          <ModalHeader>Delete Profile Picture</ModalHeader>
          <ModalBody>
            <p>Are you sure you want to delete your profile picture? This action cannot be undone.</p>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="flat"
              onClick={() => setShowDeleteModal(false)}
              isDisabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              color="danger"
              onClick={handleDelete}
              isLoading={isDeleting}
              startContent={!isDeleting ? <FiTrash2 /> : undefined}
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ProfilePictureUpload;

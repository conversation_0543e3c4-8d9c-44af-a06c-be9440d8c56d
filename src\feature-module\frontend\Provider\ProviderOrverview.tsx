import React from 'react';
import { FaRegStar, FaRegStarHalfStroke, FaStar } from 'react-icons/fa6';
import { IoBagHandleOutline, IoBookmarkOutline } from 'react-icons/io5';
import { VscTools } from 'react-icons/vsc';
import CustomCardWithHeader from '../../components/CustomCardWithHeader';
import CustomChip from '../../components/CustomChip';
import { FiUser } from 'react-icons/fi';
import moment from 'moment';
import { Address, ProviderData, ServiceGet } from '../../../utils/type';
import StatusCard from '../../components/common/ui/StatusCard';
import ServiceCard from '../../components/common/ui/ServiceCard';

interface ProviderProps {
  service: ServiceGet[];
  providerData: ProviderData;
  isProviderLoading: boolean;
  isServiceLoading: boolean;
}

const ProviderOrverview = ({
  providerData,
  service,
  isProviderLoading,
  isServiceLoading,
}: ProviderProps) => {
  const InfoRow = ({
    icon,
    label,
    value,
    className,
  }: {
    icon?: React.ReactNode;
    label: string;
    value: string;
    className?: string;
  }) => (
    <div className={`flex items-center justify-between  py-2 ${className}`}>
      <div className="flex items-center gap-3 text-body ">
        {icon}
        <span className="text-body">{label}</span>
      </div>
      {isProviderLoading ? (
        <div className="text-xs text-gray-400">Loading...</div>
      ) : (
        <p className="text-body-bold text-end">{value || '—'}</p>
      )}
    </div>
  );

  const formatAddress = (address?: Address) => {
    const parts = [
      address?.postalCode,
      address?.addressLine1,
      address?.city,
      address?.state,
      address?.country,
    ];

    return parts.filter(Boolean).join(', ');
  };
  console.log('error: ', service);
  return (
    <div>
      {/* Status */}
      <div className="grid grid-cols-5 gap-6 mt-10">
        <div className="col-span-3">
          <div className="grid grid-cols-5 gap-5">
            <StatusCard
              title="Rating (36)"
              value={4.5}
              isHoverEffect={true}
              icon={
                <div className="flex gap-1">
                  <FaStar className="text-xl fill-yellow-400" />
                  <FaStar className="text-xl fill-yellow-400" />
                  <FaStar className="text-xl fill-yellow-400" />
                  <FaRegStarHalfStroke className="text-xl fill-yellow-400" />
                  <FaRegStar className="text-xl text-gray-400" />
                </div>
              }
            />

            <StatusCard
              title="Jobs"
              isHoverEffect={true}
              value={178}
              icon={<VscTools className="text-2xl text-primary" />}
            />

            <StatusCard
              title="Total Service"
              isHoverEffect={true}
              value={service?.length || '-'}
              icon={<IoBagHandleOutline className="text-2xl text-primary" />}
            />

            <StatusCard
              title="Inprogress Booking"
              isHoverEffect={true}
              value={27}
              icon={<IoBookmarkOutline className="text-2xl text-primary" />}
            />

            <StatusCard
              title="Staff"
              value={3}
              isHoverEffect={true}
              icon={<FiUser className="text-2xl text-primary" />}
            />
          </div>

          <CustomCardWithHeader
            title="About us"
            subtitle="Our values and commitment to excellence"
            isHeaderDevider={true}
            className="mt-6"
            mainContent={
              <>
                {isProviderLoading ? (
                  <div className="text-xs text-gray-400">Loading...</div>
                ) : (
                  <p className="text-xs text-gray-600 leading-5 -mt-3">
                    {providerData?.bio || '-'}
                  </p>
                )}
              </>
            }
          />
        </div>

        <div className="col-span-2">
          <CustomCardWithHeader
            title="  Provider Information"
            subtitle=" provider details and contact information"
            isHeaderDevider={true}
            mainContent={
              <>
                <div className="flex items-center justify-between mb-1 -mt-4">
                  <div className="flex items-center gap-3 text-sm text-gray-500">
                    {/* {icon} */}
                    <span className="text-body">Category</span>
                  </div>
                  <div className="space-x-1 space-y-1 max-w-lg">
                    <CustomChip
                      label={'Information technology'}
                      color="primary"
                      size="sm"
                      variant="flat"
                      className="text-xs "
                    />
                    <CustomChip
                      label={'Marketing'}
                      color="primary"
                      size="sm"
                      variant="flat"
                      className="text-xs "
                    />
                    <CustomChip
                      label={'Shoping'}
                      color="primary"
                      size="sm"
                      variant="flat"
                      className="text-xs "
                    />
                  </div>
                </div>

                <InfoRow label="Email" value={providerData?.email || '-'} />
                <InfoRow
                  label="Location"
                  value={formatAddress(providerData?.address) || '-'}
                />
                <InfoRow
                  label="Date of Join"
                  value={
                    moment(providerData?.createdAt).format('MMM DD, YYYY') ||
                    '-'
                  }
                />
              </>
            }
          />

          <CustomCardWithHeader
            title="Contact Information"
            subtitle=" provider details and contact information"
            isHeaderDevider={true}
            className="mt-3"
            mainContent={
              <>
                <InfoRow
                  label="Location"
                  value={formatAddress(providerData?.address) || '-'}
                  className="-mt-4"
                />
                <InfoRow
                  label="Contact No"
                  value={
                    moment(providerData?.createdAt).format('MMM DD, YYYY') ||
                    '-'
                  }
                />
              </>
            }
          />
        </div>
      </div>

      <div className="mt-10 ">
        <div className="flex flex-col items-start">
          <p className="text-md font-semibold text-gray-700 ">
            Services We Provide
          </p>
          <p className="text-caption mb-2">
            Explore how our comprehensive services help you achieve your goals
            efficiently and effectively
          </p>
        </div>
        <div className="border-b"></div>

        <div className="mt-6 grid grid-cols-5 gap-6">
          {isServiceLoading ? (
            <>
              <div className="text-xs text-gray-400">Loading...</div>
            </>
          ) : (
            <>
              {service?.length > 0 ? (
                service?.map((service: ServiceGet, index: number) => (
                  <div key={index}>
                    <ServiceCard data={service} />
                  </div>
                ))
              ) : (
                <small className="col-span-5 justify-center items-center pl-5">
                  No Service
                </small>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProviderOrverview;

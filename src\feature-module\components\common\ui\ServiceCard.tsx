/* eslint-disable @typescript-eslint/no-explicit-any */
import { Avatar, Card, CardBody, CardHeader, Image } from '@heroui/react';
import { SlLocationPin } from 'react-icons/sl';
import { FaRegBookmark, FaStar } from 'react-icons/fa6';
import { TbRosetteDiscountCheckFilled } from 'react-icons/tb';
import { all_routes } from '../../../../core/data/routes/all_routes';
import { Link, useNavigate } from 'react-router-dom';
import { useQueries } from '@tanstack/react-query';
import { getCategoryById } from '../../../../service/categoryService';
import { PackageGet, ServiceGet } from '../../../../utils/type';
import CustomButton from '../../CustomButton';

const ServiceCard = ({ data }: { data: ServiceGet }) => {
  const navigate = useNavigate();
  // const { t } = useTranslation();
  const routes = all_routes;
  console.log('Data: ', data);
  const categoryQueries = useQueries({
    queries: [
      {
        queryKey: ['GET_CATEGORY_BY_ID', data.categoryId],
        queryFn: () => getCategoryById(data.categoryId),
        enabled: !!data.categoryId,
        staleTime: 1 * 60 * 1000, // 1 minute
        refetchOnWindowFocus: false,
      },
      // Add other queries if needed
    ],
  });
  console.log('ID: ', data?.providerId);
  // Create a map of category IDs to their names
  const categoryMap = categoryQueries.reduce(
    (acc, query) => {
      const categoryId = query?.data?.category?.categoryId; // Ensure we get categoryId from the query result
      if (categoryId && query.data) {
        const categoryData = query.data as {
          category: { categoryName: string };
        };
        acc[categoryId] = categoryData.category.categoryName;
      }
      return acc;
    },
    {} as Record<string, string>
  );

  const handleNavigate = (id: string) => {
    if (!id) return;
    // const uid = auth?.user?.profile?.preferred_username || 'No name';
    // const providerId = data?.serviceInfo?.providerId || 'No provider ID';

    //  if (!auth?.isAuthenticated) {
    //    setIsModalOpen(true);
    //    return;
    //  }

    // if (uid === providerId) {
    //   toast.error('You cannot book your own service');
    //   return;
    // }

    navigate(routes.serviceBooking, {
      state: {
        serviceId: id,
      },
    });
  };

  const getPackageLowestPrice = (packages: PackageGet[] | undefined) => {
    if (!packages || packages.length === 0) return undefined;
    return packages.reduce((lowest, pkg) => {
      return pkg.price < lowest ? pkg.price : lowest;
    }, packages[0].price);
  };

  const calculateDiscount = (
    price?: string,
    discount?: string,
    type?: 'amount' | 'percentage' | undefined
  ) => {
    if (!price) return 0;
    const priceNum = Number(price);
    if (!discount || !type) return priceNum;

    const discountNum = Number(discount);
    return type === 'amount'
      ? Math.max(priceNum - discountNum, 0)
      : Math.max(priceNum - (priceNum * discountNum) / 100, 0);
  };

  const navigateToProfile = (id: string) => {
    navigate(`/provider/provider-profile/${id}`);
  };

  return (
    <>
      <div className="cursor-pointer border-1 border-secondary/50  transition-all duration-300 ease-in-out w-full min-h-[350px] max-h-[350px] rounded-lg">
        <div className="relative rounded-lg">
          <div className="bg-gray-200 flex justify-center items-center cursor-pointer w-full h-[200px] relative overflow-hidden rounded-t-lg">
            <Link
              to={`${routes.serviceDetails}/${data.serviceId}/${data.slug}`}
            >
              <Image
                src={
                  data?.gallery[0]?.serviceImages[0] ||
                  'https://cdn.staging.gigmosaic.ca/common/fallbackimg.jpg'
                }
                alt={data?.serviceTitle}
                className="object-cover w-full h-full rounded-t-lg"
                shadow="none"
                isZoomed
                radius="none"
              />
            </Link>
          </div>

          <div className="absolute top-0 ledt-0 p-3 z-20">
            <small className=" bg-gray-200 text-gray-600  px-2 py-1 rounded-md text-[11px]">
              {categoryMap[data.categoryId] || 'Unknown Category'}
            </small>
          </div>

          {data?.isPackage && data?.packages?.length
            ? data.packages.some((p) => p.isDiscount) && (
                <div className="absolute top-0 right-0 p-2 z-20">
                  <small className="flex items-center text-[11px] bg-green-300 text-gray-700 px-2 py-[2px] rounded-full font-semibold">
                    <TbRosetteDiscountCheckFilled className="mr-1 text-xs text-green-700" />
                    Discount Available
                  </small>
                </div>
              )
            : data?.isDiscount && (
                <div className="absolute top-0 right-0 p-2 z-20">
                  <small className="flex items-center text-[11px] bg-green-300 text-gray-700 px-2 py-[2px] rounded-full font-semibold">
                    <TbRosetteDiscountCheckFilled className="mr-1 text-xs text-green-700" />
                    {`${data?.discount?.valueType !== 'percentage' ? '$' : ''}${data?.discount?.amount}${data?.discount?.valueType === 'percentage' ? '%' : ''} Save`}
                  </small>
                </div>
              )}
        </div>

        <Card radius="none" shadow="none" className="  bg-transparent">
          <CardHeader className="pb-0 pt-2 px-4 flex-col items-start"></CardHeader>
          <CardBody className="overflow-visible -mt-3 pb-3">
            <div className="flex justify-between items-center">
              <div className="flex flex-initial mb-1 items-center ">
                <div>
                  <SlLocationPin size={12} className="mr-1 text-secondary" />
                </div>

                <small className="text-caption line-clamp-1">
                  {data.location[0]?.city}, {data.location[0]?.state}
                </small>
              </div>

              {data?.isPackage && (
                <div className="">
                  <small className=" bg-purple-100 text-gray-600 font-semibold  px-2 py-1 rounded-md text-[10px]">
                    Package Includes
                  </small>
                </div>
              )}
            </div>
            <Link
              to={`${routes.serviceDetails}/${data.serviceId}/${data.slug}`}
            >
              <p className="text-[13px] font-semibold text-gray-700 line-clamp-1 mt-1">
                {data?.serviceTitle || 'No title'}
              </p>
            </Link>

            <div>
              <div className="flex items-center justify-between mt-2 py-1 pl-2">
                {/* Rating */}
                <div className="flex flex-initial justify-center items-end -ml-2">
                  <FaStar className="text-yellow-500  mr-1 " size={14} />
                  <small className="-mb-1 font-medium ">
                    4.5 <span className="text-caption "> (366)</span>
                  </small>
                </div>

                {/* Price */}
                <div className="flex items-baseline gap-2">
                  {data?.isPackage && (
                    <p className="text-[11px] font-semibold text-secondary items-end">
                      Start from
                    </p>
                  )}
                  <div className="flex items-baseline gap-1">
                    {data?.isPackage ? (
                      <>
                        <p className="text-md font-bold text-gray-700">
                          ${getPackageLowestPrice(data?.packages) || '-'}
                        </p>
                      </>
                    ) : (
                      <>
                        {data?.isDiscount && !data?.isPackage ? (
                          <>
                            <p className="text-md font-bold text-gray-700">
                              $
                              {calculateDiscount(
                                String(data?.price),
                                String(data?.discount?.amount),
                                data?.discount?.valueType as
                                  | 'amount'
                                  | 'percentage'
                                  | undefined
                              )}
                            </p>
                            <small className="text-gray-400 line-through">
                              ${data?.price || '-'}
                            </small>
                          </>
                        ) : (
                          <p className="text-md font-bold text-gray-700">
                            ${data?.price || '-'}
                          </p>
                        )}
                      </>
                    )}
                    {/* <small className="text-gray-400 line-through">$12000</small> */}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <div className="flex items-end justify-between mt-3 ">
                {/* <Link to={`/provider/provider-profile/${data?.providerId}`}> */}
                <button
                  onClick={() => navigateToProfile(data?.providerId)}
                  className="flex flex-initial cursor-pointer"
                >
                  <Avatar
                    showFallback
                    classNames={{
                      icon: 'text-gray-500 ',
                      base: 'border border-gray-600',
                    }}
                    name="AC"
                    size="sm"
                    src="https://cdn.staging.gigmosaic.ca/provider/G_UID_73-Test/profile-picture/1756382877396-EuQb8U2zAekWXhTyYyqwRRj5D8B5uVKbZ0xL0ROJfrhLQEOYmnQsSJkvH6Hl0k5uO0IMdo2Zt1Nj6Crq9VtC4q8neEFSCe0N4ItIK5TDM8280JXtfuhpjZSjEgXjFvqY.jpg"
                    className="bg-purple-100 hover:ring-1 hover:ring-purple-200"
                  />
                  <div className="flex items-baseline justify-start flex-col ml-1.5">
                    <p className="text-[11px] text-gray-700 hover:underline hover:underline-offset-1">
                      Auto Car Repaire
                    </p>
                    <p className="item-start text-[11px] text-gray-500">
                      Ontario
                    </p>
                  </div>
                </button>
                {/* </Link> */}
                <CustomButton
                  // label={t('services.0.button')}
                  label="Book Now"
                  variant="flat"
                  color="primary"
                  className=""
                  size="sm"
                  startContent={<FaRegBookmark />}
                  onPress={() => handleNavigate(data?.serviceId)}
                />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </>
  );
};

export default ServiceCard;

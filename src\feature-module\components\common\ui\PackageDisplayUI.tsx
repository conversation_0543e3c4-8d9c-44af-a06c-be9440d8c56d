import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, Chip } from '@heroui/react';
import { HiCheck } from 'react-icons/hi';
import {
  TbRosetteDiscountCheckFilled,
  TbRosetteDiscountOff,
} from 'react-icons/tb';

export interface Discount {
  amount: string;
  valueType: 'amount' | 'percentage';
}

export interface PackageGet {
  packageId: string;
  packageName: string;
  price: string;
  includes?: string[] | { [key: string]: string }; // flexible type
  isDiscount?: boolean;
  discount?: Discount;
  isSoldOut?: boolean;
}

type Props = {
  data: PackageGet[];
};

const PackageDisplayUI = ({ data }: Props) => {
  if (!data || data.length === 0) return null;

  // Normalize includes (supports array or object)
  const formatIncludes = (includes?: string[] | { [key: string]: string }) => {
    if (!includes) return [];
    if (Array.isArray(includes)) {
      return includes.filter((i) => i.trim() !== '');
    }
    return Object.values(includes)
      .map((item) => item.trim())
      .filter((item) => item !== '');
  };

  const calculateDiscount = (
    price?: string,
    discount?: string,
    type?: 'amount' | 'percentage'
  ) => {
    if (!price) return 0;
    const priceNum = Number(price);
    if (!discount || !type) return priceNum;

    const discountNum = Number(discount);
    return type === 'amount'
      ? Math.max(priceNum - discountNum, 0)
      : Math.max(priceNum - (priceNum * discountNum) / 100, 0);
  };

  return (
    <div className="space-y-4">
      {data.map((item, index) => {
        const includes = formatIncludes(item.includes);
        const finalPrice = calculateDiscount(
          item.price,
          item.discount?.amount,
          item.discount?.valueType
        );

        return (
          <Card
            key={index}
            radius="sm"
            isDisabled={item?.isSoldOut}
            shadow="none"
            className={`border-1 cursor-pointer transition-colors ${
              item.isSoldOut
                ? 'border-red-100 bg-red-50'
                : 'border-secondary/30 hover:gradient-primary'
            }`}
          >
            <CardHeader className="py-3 px-4 flex flex-col items-start justify-start gap-2">
              {/* Title + Chip */}
              <div className="flex items-start justify-between w-full">
                <h2 className="text-second-heading line-clamp-1">
                  {item.packageName || 'No Name'}
                </h2>

                {item.isSoldOut ? (
                  <Chip
                    color="danger"
                    variant="flat"
                    size="sm"
                    startContent={<TbRosetteDiscountOff className="text-lg" />}
                  >
                    Sold Out
                  </Chip>
                ) : item.isDiscount && item.discount ? (
                  <Chip
                    color="success"
                    variant="flat"
                    size="sm"
                    startContent={
                      <TbRosetteDiscountCheckFilled className="text-lg" />
                    }
                  >
                    {item.discount.valueType === 'percentage'
                      ? `${item.discount.amount}% Save`
                      : `$${item.discount.amount} Save`}
                  </Chip>
                ) : null}
              </div>

              {/* Pricing */}
              <div className="-mt-1">
                <div className="flex jus items-baseline gap-2">
                  <p className="text-lg font-semibold text-gray-600">
                    ${finalPrice.toFixed(2)}
                  </p>
                  {item.isDiscount && (
                    <p className="text-sm font-medium text-gray-500 line-through">
                      ${Number(item.price).toFixed(2)}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>

            {/* Includes */}
            {includes.length > 0 && (
              <CardBody className="border-t px-4 pb-3 pt-2 -mt-2">
                <ul className="space-y-2">
                  {includes.map((inc, i) => (
                    <li key={i} className="flex items-center space-x-2">
                      <HiCheck className="text-green-500 w-4 h-4 flex-shrink-0" />
                      <span className="text-body text-gray-700">{inc}</span>
                    </li>
                  ))}
                </ul>
              </CardBody>
            )}
          </Card>
        );
      })}
    </div>
  );
};

export default PackageDisplayUI;

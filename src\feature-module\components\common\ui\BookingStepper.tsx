import { BsCalendar2Check } from 'react-icons/bs';
import { FaRegCreditCard } from 'react-icons/fa6';

import { IoBagHandleOutline } from 'react-icons/io5';
import { TfiLocationPin } from 'react-icons/tfi';

type StepperProps = {
  step: number;
};

// 1 - date and time
// 2 - addtionalService
// 3 - personal details
// 4 - payment

const BookingStepper = ({ step }: StepperProps) => {
  return (
    <div>
      <div className="mt-3 border-b border-gray-300 pb-2">
        <ul className="grid min-[400px]:grid-cols-2 md:grid-cols-4 gap-6">
          {/* Date & Time */}
          <li>
            {step >= 1 ? (
              <CompleteIcon />
            ) : (
              <span className="flex h-9 w-9 mb-4 items-center justify-center rounded-full bg-gray-200 ring-8 ring-white">
                <BsCalendar2Check />
              </span>
            )}

            <h4 className="mb-1 text-sm font-semibold text-primary">
              {' '}
              Date & Time
            </h4>
          </li>

          {/* Additonal Service */}
          <li>
            {step >= 2 ? (
              <CompleteIcon />
            ) : (
              <span className="flex h-9 w-9 mb-4 items-center justify-center rounded-full bg-gray-200 ring-8 ring-white">
                <IoBagHandleOutline size={18} />
              </span>
            )}

            <h4 className="mb-1 text-sm font-semibold text-primary">
              Addtional Service
            </h4>
          </li>

          {/* Address */}
          <li>
            {step >= 3 ? (
              <CompleteIcon />
            ) : (
              <span className="flex h-9 w-9 mb-4 items-center justify-center rounded-full bg-gray-200 ring-8 ring-white">
                <TfiLocationPin size={18} />
              </span>
            )}

            <h4 className="mb-1 text-sm font-semibold text-primary">
              Address Information
            </h4>
          </li>

          {/* Payment */}
          <li>
            {step >= 4 ? (
              <CompleteIcon />
            ) : (
              <span className="flex h-9 w-9 mb-4 items-center justify-center rounded-full bg-gray-200 ring-8 ring-white">
                <FaRegCreditCard />
              </span>
            )}

            <h4 className="mb-1 text-sm font-semibold text-primary">Payment</h4>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default BookingStepper;

export const CompleteIcon = () => {
  return (
    <span className="flex h-9 w-9 mb-4 items-center justify-center rounded-full bg-blue-200 ring-8 ring-white">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-4 w-4 fill-blue-700"
        viewBox="0 0 24 24"
      >
        <path
          d="M22.05 4.8c-.6-.6-1.5-.6-2.1 0L8.7 16.05 4.05 11.4c-.6-.6-1.5-.6-2.1 0s-.6 1.5 0 2.1l5.7 5.7c.******** 1.05.45s.75-.15 1.05-.45l12.3-12.3c.6-.6.6-1.5 0-2.1z"
          data-original="#000000"
        />
      </svg>
    </span>
  );
};

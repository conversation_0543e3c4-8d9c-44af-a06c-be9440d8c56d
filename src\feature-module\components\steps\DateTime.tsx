// import Calendar from 'react-calendar';
// import { DayPicker } from 'react-day-picker';
// import 'react-day-picker/style.css';
// import { useDispatch, useSelector } from 'react-redux';
// import { RootState } from '../../../core/data/redux/reducer';
// import {
//   resetSelectedTime,
//   updateDateTime,
//   updateStaff,
// } from '../../../core/data/redux/booking/bookingSlice';
// import { useEffect, useState } from 'react';
// import { Avatar, Spinner } from '@heroui/react';
// import { useCheckBookingAvailability } from '../../../hook/useMutationData';
// import moment from 'moment';
// import { apiClient } from '../../../api';
// import { toast } from 'react-toastify';

// type TimeSlot = {
//   booked: number;
//   bookedStaffId: number;
//   bookingDate: string;
//   bookingId: number;
//   bookingStatus: string;
//   from: string;
//   maxBookings: number;
//   staffStatus: string;
//   to: string;
//   timeSlotId: string;
//   availableStaff?: Staff[];
// };

// type Staff = {
//   staffId: string;
//   name: string;
//   fullName: string;
//   city: string;
//   numberOfCompletedServices?: number;
//   providerId?: string;
// };

// const DateTimeStep = () => {
//   const dispatch = useDispatch();
//   const [staffLoading, setStaffLoading] = useState<boolean>(false);
//   const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
//   const [timeSlot, setTimeSlot] = useState<TimeSlot[]>([]);
//   const [selectDate, setSelectdate] = useState<Date | undefined>(new Date());
//   const [selectedStaffId, setSelectedStaffId] = useState<string | null>(null);
//   const [staffData, setStaffData] = useState<Staff[] | undefined>(undefined);

//   const { selectedDate } = useSelector(
//     (state: RootState) => state.booking.dateTime
//   );
//   const { selectedStaff } = useSelector(
//     (state: RootState) => state.booking.staff
//   );

//   const { services, dateTime } = useSelector(
//     (state: RootState) => state.booking
//   );

//   console.log('SELECTED DATE: ', selectedDate);
//   console.log('SELECTED STAFF: ', selectedStaff);
//   console.log('ALL : ', services);

//   const { serviceBasicDetails } = services;

//   const serviceId = serviceBasicDetails[0]?.serviceId;

//   const staffInService =
//     serviceBasicDetails[0]?.staff.length > 0 ? true : false;

//   const { mutate } = useCheckBookingAvailability();

//   const weekdayToNumber: Record<string, number> = {
//     sunday: 0,
//     monday: 1,
//     tuesday: 2,
//     wednesday: 3,
//     thursday: 4,
//     friday: 5,
//     saturday: 6,
//   };

//   const allowedDays = serviceBasicDetails[0]?.availableDate.map(
//     (item: { day: string }) => weekdayToNumber[item.day.toLowerCase()]
//   );

//   const getNextAvailableDate = () =>
//     Array.from({ length: 14 }, (_, i) => {
//       const d = new Date();
//       d.setDate(d.getDate() + i);
//       return allowedDays.includes(d.getDay()) ? d : null;
//     }).find(Boolean) || new Date();

//   useEffect(() => {
//     const nextDate = getNextAvailableDate();
//     setSelectdate(selectedDate ? new Date(selectedDate) : nextDate);
//     dispatch(
//       updateDateTime({ selectedDate: moment(nextDate).format('YYYY-MM-DD') })
//     );
//     dispatch(updateStaff({ isStaffAvilableInService: staffInService }));
//   }, []);

//   useEffect(() => {
//     const fetchTimeSlotBySelectedDate = async () => {
//       try {
//         const data = {
//           serviceId,
//           date: moment(selectDate).format('YYYY-MM-DD'),
//         };

//         const res = await apiClient.post(
//           'service/api/v1/booking/checkAvailableStaffForTimeSlot',
//           data
//         );
//         console.log('RES: ', res);
//         const timeSlot = res.data?.timeSlots || [];
//         setTimeSlot(timeSlot);

//         if (timeSlot.length > 0) {
//           const selectFirestTimeSlot = timeSlot[0];

//           dispatch(
//             updateDateTime({
//               selectedFromTime: selectFirestTimeSlot.from,
//               selectedToTime: selectFirestTimeSlot.to,
//               timeSlotId: selectFirestTimeSlot.timeSlotId,
//             })
//           );

//           setStaffData(selectFirestTimeSlot.availableStaff || []);

//           const selectedStaffByFirstIndex =
//             selectFirestTimeSlot.availableStaff?.[0]?.staffId || null;

//           const alreadySelectTimeSlotId = dateTime?.timeSlotId || null;

//           setSelectedSlot(
//             alreadySelectTimeSlotId || selectFirestTimeSlot.timeSlotId
//           );

//           const staffIdToSet = selectedStaff || selectedStaffByFirstIndex;
//           setSelectedStaffId(staffIdToSet);
//           dispatch(updateStaff({ selectedStaff: staffIdToSet }));
//         }
//       } catch (error) {
//         console.error('Error fetching staff:', error);
//         toast.error('Something went wrong while fetching staff');
//       } finally {
//         setStaffLoading(false);
//       }
//     };

//     fetchTimeSlotBySelectedDate();
//   }, [selectDate, serviceId]);

//   console.log('SELECTED DATE: ', selectDate);
//   console.log('SELECTED STAFF ID: ', selectedStaffId);
//   console.log('SELECTED TIME SLOT: ', selectedSlot);

//   //HANDLE DATE CHANGE
//   const handleDateChange = async (value: Date | Date[] | null) => {
//     if (value instanceof Date) {
//       setSelectdate(value); // local state
//       const date = moment(value).format('YYYY-MM-DD');

//       if (date) {
//         dispatch(resetSelectedTime());
//         dispatch(updateDateTime({ selectedDate: date }));
//       } else {
//         dispatch(resetSelectedTime());
//         console.error('Date selection has some issue');
//         toast.error('Date selection has some issue. trying again!');
//       }
//     }
//   };

//   const selectTimeSlot = (slotId: string, from: string, to: string) => {
//     if (timeSlot && from && to) {
//       const foundTimeSlot = timeSlot.find((slot) => slot.timeSlotId === slotId);
//       setSelectedSlot(foundTimeSlot?.timeSlotId ?? null);
//       console.log('SELECTED TIME SLOT: ', foundTimeSlot);
//       setStaffData(foundTimeSlot?.availableStaff || []);
//       dispatch(
//         updateDateTime({
//           selectedFromTime: from,
//           selectedToTime: to,
//           timeSlotId: slotId,
//         })
//       );
//     }
//   };

//   const selectStaff = (staffId: string) => {
//     if (staffId) {
//       setSelectedStaffId(staffId);

//       dispatch(updateStaff({ selectedStaff: staffId }));

//       if (selectDate && serviceId) {
//         mutate({
//           serviceId,
//           date: selectDate.toISOString().split('T')[0],
//           staffId,
//         });
//       }
//     }
//   };

//   console.log('Time slot: ', timeSlot);
//   return (
//     <>
//       <div className="grid grid-cols-4 gap-20">
//         <div>
//           <h3 className="text-sm font-bold mb-4 -mt-5">Select date</h3>
//           {/* <Calendar
//             className="lg:w-[100px] rounded-lg shadow-md p-3 bg-white custom-calendar"
//             onChange={(value: any) =>
//               handleDateChange(value as Date | Date[] | null)
//             }
//             value={selectDate}
//             minDate={new Date()}
//             tileDisabled={({ date }) => {
//               return !allowedDays.includes(date.getDay());
//             }}
//           /> */}

//           <DayPicker
//             mode="single"
//             animate
//             navLayout="around"
//             // numberOfMonths={2}
//             // modifiers={
//             //   {
//             //     // booked: bookedDays,
//             //     //   bookedDays: bookedDayss,
//             //   }
//             // }
//             modifiersClassNames={{
//               booked: 'my-booked-class',
//               today: 'calendar-today',
//               //   bookedDays: "calendar-holiday",
//               //   selected: "rounded-xl bg-blue-500 text-white",
//             }}
//             // defaultMonth={bookedDays}
//             // hidden={bookedDays}
//             // disabled={{ before: new Date(), dayOfWeek: unAvailableDates }}
//             // disabled={{ before: new Date() }}
//             // excludeDisabled

//             // selected={selected}
//             // onSelect={setSelected}
//             required
//             // footer={
//             //   selected
//             //     ? `Selected: ${selected.toLocaleDateString()}`
//             //     : "Pick a day."
//             // }
//           />
//         </div>

//         {/* Right Column - Time Slots */}
//         <div className="w-full col-span-3 ">
//           {/* {staffInService && !isStaffAvilableInDay ? (
//               <p></p>
//             ) : ( */}
//           <>
//             <h3 className="text-md font-semibold text-gray-700 mb-4">
//               Select Time
//             </h3>
//             {/* {timeSlotLoading ? (
//               <div className="flex justify-center items-center">
//                 <Spinner size="md" color="primary" />
//               </div>
//             ) : ( */}
//             <div className="col-span-2">
//               <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-2">
//                 {timeSlot?.map((slot, index) => (
//                   <button
//                     key={index}
//                     disabled={slot.booked >= slot.maxBookings}
//                     onClick={() =>
//                       selectTimeSlot(slot.timeSlotId, slot.from, slot.to)
//                     }
//                     className={`rounded-md border px-4 py-1 text-left shadow-sm transition-all duration-200 ${
//                       selectedSlot === slot.timeSlotId
//                         ? 'bg-blue-200 text-gray-500 border-gray-500 '
//                         : 'bg-green-100 hover:shadow-md hover:bg-green-200 hover:border-green-400 border-green-300 '
//                     }`}
//                   >
//                     <div className="text-sm font-semibold text-gray-800">
//                       {slot.from} - {slot.to}
//                     </div>

//                     <div className="mt-2 flex flex-col space-y-1 ">
//                       <div className="text-xs font-semibold text-gray-600">
//                         {/* {slot.availableStaff.length} Staff */}
//                       </div>
//                     </div>
//                   </button>
//                 ))}
//               </div>
//             </div>
//             {/* )} */}
//           </>
//           {/* )} */}

//           <div className="w-full mt-5 ">
//             {/* No Staff in service */}
//             {staffInService && (
//               <>
//                 <h3 className="text-md font-semibold text-gray-700 mb-4">
//                   Select Staff
//                 </h3>
//                 {staffLoading ? (
//                   <div className="flex justify-center items-center">
//                     <Spinner size="md" color="primary" />
//                   </div>
//                 ) : (
//                   <>
//                     <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 mb-8">
//                       {staffData?.map((s, index) => (
//                         <button
//                           key={index}
//                           onClick={() => selectStaff(s.staffId)}
//                           className={`rounded-md border px-4 py-1 text-left shadow-sm transition-all duration-200 ${
//                             selectedStaffId === s.staffId
//                               ? 'bg-purple-300 text-gray-500 border-gray-500 '
//                               : 'bg-purple-50 hover:shadow-md hover:bg-purple-300 hover:border-purple-300 border-gray-300'
//                           }`}
//                         >
//                           <div className="flex justify-start items-center py-1">
//                             <Avatar name="Junior" size="md" radius="sm" />
//                             <div className=" justify-start items-center mt-2 mb-2 ml-2">
//                               <p className="text-sm font-semibold text-gray-700">
//                                 {s?.fullName}
//                               </p>
//                             </div>
//                           </div>
//                         </button>
//                       ))}
//                     </div>
//                   </>
//                 )}
//               </>
//             )}
//           </div>
//         </div>
//       </div>
//     </>
//   );
// };

// export default DateTimeStep;

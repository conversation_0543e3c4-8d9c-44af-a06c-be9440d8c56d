import { useEffect, useState } from 'react';
import { DayPicker } from 'react-day-picker';
import 'react-day-picker/style.css';
import { useCheckTimesSlotAvailability } from '../../../hook/useMutationData';
import SmallLoadingSpinner from '../../components/common/ui/SmallLoadingSpinner';
import { toast } from 'react-toastify';
import moment from 'moment';
import { LuClock3, LuUser } from 'react-icons/lu';
import { AvailabilityGet, ServiceGet, TimeSlotGet } from '../../../utils/type';
import { FaRegCalendarCheck } from 'react-icons/fa6';

type DateAndStaffProps = {
  data: ServiceGet;
  onChange: (data: ITimeAndStaff) => void;
};

interface ITimeAndStaff {
  date: string;
  timeSlotId: string;
  time: string;
  staffId: string;
  staff: StaffMember;
}

type weekType = {
  day: string;
  key: number;
};

type StaffMember = {
  staffId: string;
  fullName: string;
  providerStaffId: string;
  isProvider: boolean;
};

const week: weekType[] = [
  { day: 'sunday', key: 0 },
  { day: 'monday', key: 1 },
  { day: 'tuesday', key: 2 },
  { day: 'wednesday', key: 3 },
  { day: 'thursday', key: 4 },
  { day: 'friday', key: 5 },
  { day: 'saturday', key: 6 },
];

const DateAndStaff = ({ data, onChange }: DateAndStaffProps) => {
  const [selected, setSelected] = useState<Date>();
  const [unAvailableDates, setUnAvailableDates] = useState<number[]>([0]);
  const [timeSlots, setTimeSlots] = useState<TimeSlotGet[]>([]);
  const [selectTimeSlot, setSelectTimeSlot] = useState<string>('');
  const [selectedStaffId, setSelectedStaffId] = useState<string>('');
  const [availableStaff, setAvailableStaff] = useState<StaffMember[] | null>(
    null
  );

  console.log('TIME SLOT: ', timeSlots);

  const {
    mutateAsync,
    isPending,
    isError: isCheckTimeslotError,
    error: checkTimeslotError,
  } = useCheckTimesSlotAvailability();

  const endMonth = new Date();
  endMonth.setMonth(endMonth.getMonth() + 1);
  const today = new Date();

  // CHECK AVAILABILITY TIMESLOT
  useEffect(() => {
    const checkBookingAvailableTimeSlot = async () => {
      const serviceId = data?.serviceId;

      if (!serviceId) return;
      if (!selected) {
        console.error('No date selected');
        return;
      }

      setSelectTimeSlot('');
      setAvailableStaff(null);
      setSelectedStaffId('');

      const payload = {
        serviceId,
        date: moment(selected).format('YYYY-MM-DD'),
      };

      try {
        const response = await mutateAsync(payload);
        console.log('RESPONSE: ', response);
        if (response.success) {
          setTimeSlots(response.timeSlots);

          setSelectTimeSlot(response.timeSlots[0].timeSlotId);
        } else {
          setTimeSlots([]);
        }

        if (!response?.success) {
          console.error('Error check timeslot availability:', response);
          toast.error(
            response?.message ||
              'Something went wrong while check timeslot availability'
          );
        }
      } catch (error) {
        console.error('Error check timeslot availability: ', error);
      }
    };

    checkBookingAvailableTimeSlot();
  }, [mutateAsync, data?.serviceId, selected]);

  // STAFF
  useEffect(() => {
    try {
      setSelectedStaffId('');
      const matchedSlot = timeSlots.find(
        (slot: TimeSlotGet) => slot.timeSlotId === selectTimeSlot
      );

      // matchedSlot may be a timeslot object that contains an availableStaff array
      setAvailableStaff(matchedSlot?.availableStaff || null);

      //Default select first staff
      setSelectedStaffId(matchedSlot?.availableStaff[0]?.staffId || '');
    } catch (error) {
      console.error('Error filter staff based on time slot: ', error);
    }
  }, [timeSlots, selectTimeSlot]);

  // find the next available date
  const getNextAvailableDate = (
    startDate: Date,
    blockedWeekdays: number[]
  ): Date => {
    const date = new Date(startDate);
    while (true) {
      if (!blockedWeekdays.includes(date.getDay())) {
        return date;
      }
      date.setDate(date.getDate() + 1);
    }
  };

  //HANDLE UN AVAILABLE DATES
  useEffect(() => {
    console.log('unavailable dates');
    if (!data?.availability) return;

    const availability = data?.availability;

    const availableKeys = availability.map((a: AvailabilityGet) => {
      return week.find((w: weekType) => w.day === a.day)?.key;
    });

    const unAvailableKeys = week
      .map((w) => w.key)
      .filter((key) => !availableKeys.includes(key));

    setUnAvailableDates(unAvailableKeys);

    const nextDate = getNextAvailableDate(today, unAvailableKeys);
    setSelected(nextDate);
  }, [data]);

  useEffect(() => {
    // Find the selected staff member
    const getStaff = availableStaff?.find((s: StaffMember) => {
      return s.staffId === selectedStaffId;
    });

    // Find the selected time slot
    const timeSlot = timeSlots?.find((s: TimeSlotGet) => {
      return s.timeSlotId === selectTimeSlot;
    });

    if (getStaff && timeSlot && selected) {
      onChange({
        date: moment(selected).format('YYYY-MM-DD'),
        timeSlotId: selectTimeSlot,
        staffId: selectedStaffId,
        time: `${timeSlot.from} - ${timeSlot.to}`,
        staff: getStaff,
      });
    }
  }, [
    selected,
    selectTimeSlot,
    selectedStaffId,
    onChange,
    availableStaff,
    timeSlots,
  ]);

  return (
    <div className="mt-3 border p-3 rounded-md border-primary/30 bg-gray-50">
      <div className="border-b mb-2 flex flex-initial justify-between items-center">
        <p className="text-title-bold mb-2">Choose date and time</p>
        <p className="text-title-bold mb-2 flex items-center gap-2">
          <FaRegCalendarCheck />
          {moment(selected).format('DD MMM YYYY')}
        </p>
      </div>
      <div className="grid xl:grid-cols-2 2xl:grid-cols-5 gap-10">
        <div className="2xl:col-span-2">
          {/* <p className="text-body-bold mb-4 -mt-4">Select Date</p> */}
          <DayPicker
            mode="single"
            animate
            // numberOfMonths={2}
            navLayout="around"
            modifiersClassNames={{
              selected: 'rounded-full bg-blue-400 text-white',
            }}
            defaultMonth={today}
            disabled={[{ before: today }, { dayOfWeek: unAvailableDates }]}
            startMonth={today}
            endMonth={endMonth}
            selected={selected}
            onSelect={setSelected}
            required
          />
        </div>

        <div className="2xl:col-span-3 ">
          <div className="grid gap-10">
            {/* TIME SLOT*/}
            <div className="">
              <p className="text-body-bold mb-3 mt-3">Appointment Time</p>
              <div className="grid md:grid-cols-3 xl:grid-cols-4 gap-2 ">
                {isPending ? (
                  <div className="col-span-3 justify-center items-center">
                    <SmallLoadingSpinner size="sm" />
                  </div>
                ) : isCheckTimeslotError ? (
                  <div className="col-span-3">
                    <p className="text-xs text-red-400">{checkTimeslotError}</p>
                  </div>
                ) : (
                  <>
                    {timeSlots.map((time: TimeSlotGet, index: number) => (
                      <button
                        key={index}
                        onClick={() => setSelectTimeSlot(time?.timeSlotId)}
                        className={`flex justify-center items-center border p-2 shadow-sm  rounded-md w-38 ${
                          selectTimeSlot === time?.timeSlotId
                            ? 'border-primary bg-secondary/10'
                            : 'hover-primary-gradient border-primary/30  bg-white'
                        }`}
                      >
                        <LuClock3 className="text-secondary " />
                        <p className="text-body ml-1">{`${time?.from} - ${time?.to}`}</p>
                      </button>
                    ))}
                  </>
                )}
              </div>
            </div>

            {/* STAFF*/}
            <div className="">
              <p className="text-body-bold mb-3 ">Select Staff</p>
              <div className="grid md:grid-cols-3 xl:grid-cols-4 gap-2 ">
                {availableStaff === null ? (
                  isCheckTimeslotError ? (
                    <div className="col-span-3">
                      <p className="text-xs text-red-400">
                        {checkTimeslotError}
                      </p>
                    </div>
                  ) : (
                    <div className="col-span-3 flex justify-center items-center">
                      <SmallLoadingSpinner size="sm" />
                    </div>
                  )
                ) : (
                  <>
                    {availableStaff?.map((s: StaffMember, index: number) => (
                      <button
                        key={index}
                        onClick={() => setSelectedStaffId(s?.staffId || '')}
                        className={`flex justify-start items-center border  p-2 shadow-sm rounded-md w-38 ${
                          selectedStaffId === s?.staffId
                            ? 'border-primary bg-secondary/10'
                            : 'hover-primary-gradient border-primary/30  bg-white'
                        }`}
                      >
                        <LuUser className="text-secondary" />
                        <p className="text-body ml-2">{s?.fullName}</p>
                      </button>
                    ))}
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DateAndStaff;

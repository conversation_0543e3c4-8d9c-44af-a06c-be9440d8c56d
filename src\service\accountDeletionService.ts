import { apiClient } from '../api';
import { AxiosError } from 'axios';

export interface AccountDeletionRequest {
  email: string;
  userId: string;
  scheduledDeletionDate: string;
  requestDate: string;
  status: 'pending' | 'cancelled' | 'completed';
  gracePeriodDays: number;
}

export interface AccountDeletionResponse {
  success: boolean;
  message: string;
  deletionRequest?: AccountDeletionRequest;
  errorId?: string;
}

/**
 * Schedule account for deletion with 30-day grace period
 */
export const scheduleAccountDeletion = async (
  email: string,
  userId: string,
  userProfile?: any
): Promise<AccountDeletionResponse> => {
  try {
    console.log('Scheduling account deletion for:', { email, userId });

    // Calculate deletion date (30 days from now)
    const deletionDate = new Date();
    deletionDate.setDate(deletionDate.getDate() + 30);

    const requestPayload = {
      email,
      userId,
      User: {
        id: userId,
        email,
        name: userProfile?.name || userProfile?.given_name || '',
        sub: userProfile?.sub || userId,
        preferred_username: userProfile?.preferred_username || userId
      },
      user: {
        id: userId,
        email,
        name: userProfile?.name || userProfile?.given_name || '',
        sub: userProfile?.sub || userId,
        preferred_username: userProfile?.preferred_username || userId
      },
      confirmEmail: email,
      softDelete: true,
      scheduledDeletionDate: deletionDate.toISOString(),
      gracePeriodDays: 30,
      requestType: 'soft_delete'
    };

    // Try multiple endpoints for compatibility
    const endpoints = [
      'user/api/v1/auth/scheduleAccountDeletion',
      'user/api/v1/auth/deleteAccount',
      'user/api/v1/user/scheduleDelete'
    ];

    let response;
    let lastError;

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`);
        response = await apiClient.post(endpoint, requestPayload);
        
        if (response.status === 200 || response.status === 201 || response.status === 204) {
          break;
        }
      } catch (error) {
        console.log(`Endpoint ${endpoint} failed:`, error);
        lastError = error;
        continue;
      }
    }

    if (!response) {
      throw lastError || new Error('All endpoints failed');
    }

    return {
      success: true,
      message: `Account scheduled for deletion on ${deletionDate.toLocaleDateString()}. You have 30 days to cancel this request.`,
      deletionRequest: {
        email,
        userId,
        scheduledDeletionDate: deletionDate.toISOString(),
        requestDate: new Date().toISOString(),
        status: 'pending',
        gracePeriodDays: 30
      }
    };

  } catch (error) {
    console.error('Error scheduling account deletion:', error);
    const axiosError = error as AxiosError<{ message?: string; errorId?: string }>;
    
    return {
      success: false,
      message: axiosError.response?.data?.message || 'Failed to schedule account deletion',
      errorId: axiosError.response?.data?.errorId
    };
  }
};

/**
 * Cancel scheduled account deletion
 */
export const cancelAccountDeletion = async (
  email: string,
  userId: string
): Promise<AccountDeletionResponse> => {
  try {
    console.log('Cancelling account deletion for:', { email, userId });

    const requestPayload = {
      email,
      userId,
      action: 'cancel_deletion'
    };

    const endpoints = [
      '/api/v1/auth/cancelAccountDeletion',
      '/api/v1/user/cancelDelete',
      '/api/v1/auth/reactivateAccount'
    ];

    let response;
    let lastError;

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying cancel endpoint: ${endpoint}`);
        response = await apiClient.post(endpoint, requestPayload);
        
        if (response.status === 200 || response.status === 201 || response.status === 204) {
          break;
        }
      } catch (error) {
        console.log(`Cancel endpoint ${endpoint} failed:`, error);
        lastError = error;
        continue;
      }
    }

    if (!response) {
      throw lastError || new Error('All cancel endpoints failed');
    }

    return {
      success: true,
      message: 'Account deletion cancelled successfully. Your account has been reactivated.'
    };

  } catch (error) {
    console.error('Error cancelling account deletion:', error);
    const axiosError = error as AxiosError<{ message?: string; errorId?: string }>;
    
    return {
      success: false,
      message: axiosError.response?.data?.message || 'Failed to cancel account deletion',
      errorId: axiosError.response?.data?.errorId
    };
  }
};

/**
 * Check if account has scheduled deletion
 */
export const checkAccountDeletionStatus = async (
  email: string,
  userId: string
): Promise<AccountDeletionRequest | null> => {
  try {
    console.log('Checking account deletion status for:', { email, userId });

    const response = await apiClient.get(`/api/v1/auth/deletionStatus/${userId}`);
    
    if (response.data && response.data.deletionRequest) {
      return response.data.deletionRequest;
    }

    return null;

  } catch (error) {
    console.error('Error checking account deletion status:', error);
    return null;
  }
};

/**
 * Get local deletion info from localStorage
 */
export const getLocalDeletionInfo = (): AccountDeletionRequest | null => {
  try {
    const stored = localStorage.getItem('accountDeletionScheduled');
    if (stored) {
      const data = JSON.parse(stored);
      return {
        email: data.email,
        userId: data.userId || '',
        scheduledDeletionDate: data.scheduledDate,
        requestDate: data.requestDate,
        status: 'pending',
        gracePeriodDays: 30
      };
    }
    return null;
  } catch (error) {
    console.error('Error getting local deletion info:', error);
    return null;
  }
};

/**
 * Clear local deletion info
 */
export const clearLocalDeletionInfo = (): void => {
  try {
    localStorage.removeItem('accountDeletionScheduled');
  } catch (error) {
    console.error('Error clearing local deletion info:', error);
  }
};

/**
 * Calculate days remaining until deletion
 */
export const getDaysUntilDeletion = (scheduledDate: string): number => {
  const deletion = new Date(scheduledDate);
  const now = new Date();
  const diffTime = deletion.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return Math.max(0, diffDays);
};

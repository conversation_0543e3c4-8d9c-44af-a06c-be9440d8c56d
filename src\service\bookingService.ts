/* eslint-disable @typescript-eslint/no-explicit-any */
import { apiClient } from '../api';
import { Path } from '../api/backendUrl';
import logger from '../utils/logger';

// Booking interfaces
export interface Booking {
  id: number | string;
  service: string;
  serviceName?: string; // Alternative field name for service
  status:
    | 'Pending'
    | 'Confirmed'
    | 'Finished'
    | 'Cancelled'
    | 'Completed'
    | 'Inprogress'
    | 'Rescheduled';
  date: string;
  amount: string;
  payment: string;
  paymentMethod?: string; // Alternative field name for payment
  location: string;
  provider: string;
  email: string;
  phone: string;
  actions: string[];
  providerId?: string;
  bookingId?: string;
  serviceId?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
  serviceImages?: string[]; // Array of service image URLs
  description?: string; // Service description
  duration?: string; // Service duration
  category?: string; // Service category
  personalInfo?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    streetAddress?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    notes?: string;
    address?: {
      street?: string;
      city?: string;
      state?: string;
      postalCode?: string;
      country?: string;
    };
  };
  serviceDetails?: {
    serviceTitle?: string;
    categoryId?: string;
    subCategoryId?: string;
    price?: number;
    isOffers?: boolean;
    isAdditional?: boolean;
    staff?: any;
    availableDate?: any;
  };
  referenceCode?: string; // Booking reference code
  additionalServices?: {
    id?: string;
    name?: string;
    price?: number;
    duration?: string;
    description?: string;
  }[]; // Array of additional services
  appointmentTimeFrom?: string; // Appointment start time
  appointmentTimeTo?: string; // Appointment end time
  appointmentDate?: string; // Appointment date
  bookingStatus?: string; // Detailed booking status information
}

export interface BookingsResponse {
  bookings: Booking[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface UpdateBookingStatusData {
  status:
    | 'Pending'
    | 'Confirmed'
    | 'Finished'
    | 'Cancelled'
    | 'Completed'
    | 'Inprogress'
    | 'Rescheduled';
  updatedAt?: string;
}

export interface RescheduleBookingData {
  bookingId: string;
  newDate: string; // YYYY-MM-DD
  newFrom: string; // Start time, e.g., "10:00 AM"
  newTo: string; // End time, e.g., "10:30 AM"
  staffId?: string;
  note?: string;
  performedBy?: string;
  // Backward compatibility fields (optional)
  newTime?: string; // Time range string like "10:00 AM-11:00 AM"
  appointmentDate?: string; // New appointment date
  appointmentTimeFrom?: string; // New start time
  appointmentTimeTo?: string; // New end time
  reason?: string; // Optional reason for rescheduling
  rescheduleNote?: string; // Optional note from customer
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  userId?: string;
  status?: string;
}

export interface PromoCode {
  promoCode: string;
  discountId: string;
}

/**
 * Get all bookings for a user
 */
export const getUserBookings = async (
  params: PaginationParams = {}
): Promise<BookingsResponse> => {
  try {
    const { page = 1, limit = 10, userId, status } = params;
    console.log(
      `Fetching bookings - page: ${page}, limit: ${limit}, userId: ${userId}, status: ${status}`
    );

    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (userId) {
      queryParams.append('userId', userId);
    }

    if (status) {
      queryParams.append('status', status);
    }

    const response = await apiClient.get(
      `service/api/v1/booking?${queryParams.toString()}`
    );
    console.log('Bookings fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching bookings:', error);
    logger.error('Error fetching bookings:', error);
    throw error;
  }
};

/**
 * Get a specific booking by ID
 */
export const getBookingById = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Fetching booking with ID: ${bookingId}`);
    const response = await apiClient.get(`service/api/v1/booking/${bookingId}`);
    console.log('Booking fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching booking ${bookingId}:`, error);
    logger.error(`Error fetching booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Update booking status
 */
export const updateBookingStatus = async (
  bookingId: string,
  data: UpdateBookingStatusData
): Promise<Booking> => {
  try {
    console.log(`Updating booking ${bookingId} status to:`, data.status);

    const updatePayload = {
      ...data,
      updatedAt: data.updatedAt || new Date().toISOString(),
    };

    const response = await apiClient.put(
      `service/api/v1/booking/${bookingId}`,
      updatePayload
    );
    console.log('Booking status updated successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating booking ${bookingId} status:`, error);
    logger.error(`Error updating booking ${bookingId} status:`, error);
    throw error;
  }
};

/**
 * Confirm a booking (change status from Pending to Confirmed)
 */
export const confirmBooking = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Confirming booking: ${bookingId}`);
    return await updateBookingStatus(bookingId, { status: 'Confirmed' });
  } catch (error: any) {
    console.error(`Error confirming booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Mark booking as finished (typically after review submission)
 */
export const finishBooking = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Marking booking as finished: ${bookingId}`);
    return await updateBookingStatus(bookingId, { status: 'Finished' });
  } catch (error: any) {
    console.error(`Error finishing booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Cancel a booking
 */
export const cancelBooking = async (bookingId: string): Promise<Booking> => {
  try {
    console.log(`Cancelling booking: ${bookingId}`);
    return await updateBookingStatus(bookingId, { status: 'Cancelled' });
  } catch (error: any) {
    console.error(`Error cancelling booking ${bookingId}:`, error);
    throw error;
  }
};

/**
 * Reschedule a booking with new date and time
 */
export const rescheduleBooking = async (
  rescheduleData: RescheduleBookingData
): Promise<Booking> => {
  try {
    console.log(`Rescheduling booking ${rescheduleData.bookingId} to:`, {
      newDate: rescheduleData.newDate,
      newFrom: rescheduleData.newFrom,
      newTo: rescheduleData.newTo,
      staffId: rescheduleData.staffId,
      note: rescheduleData.note,
      performedBy: rescheduleData.performedBy,
    });

    // If only newTime provided, derive newFrom/newTo
    let newFrom = rescheduleData.newFrom;
    let newTo = rescheduleData.newTo;
    if (
      (!newFrom || !newTo) &&
      rescheduleData.newTime &&
      rescheduleData.newTime.includes('-')
    ) {
      const [startTime, endTime] = rescheduleData.newTime.split('-');
      newFrom = newFrom || startTime.trim();
      newTo = newTo || endTime.trim();
    }

    // Prepare payload per backend requirements
    const reschedulePayload = {
      newDate: rescheduleData.newDate,
      newFrom,
      newTo,
      staffId: rescheduleData.staffId,
      note:
        rescheduleData.note ||
        rescheduleData.rescheduleNote ||
        rescheduleData.reason,
      performedBy: rescheduleData.performedBy,
    };

    console.log('Sending reschedule payload:', reschedulePayload);
    console.log(
      'API URL will be:',
      `service/api/v1/booking/${rescheduleData.bookingId}/reschedule`
    );

    const response = await apiClient.put(
      `service/api/v1/booking/${rescheduleData.bookingId}/reschedule`,
      reschedulePayload
    );
    console.log('Booking rescheduled successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(
      `Error rescheduling booking ${rescheduleData.bookingId}:`,
      error
    );
    logger.error(
      `Error rescheduling booking ${rescheduleData.bookingId}:`,
      error
    );
    throw error;
  }
};

/**
 * Get available time slots for a specific date and provider
 */
export const getAvailableTimeSlots = async (
  providerId: string,
  date: string
): Promise<{
  date: string;
  timeSlots: Array<{
    time: string;
    available: boolean;
    reason?: string;
    popularity?: 'low' | 'medium' | 'high';
  }>;
}> => {
  try {
    console.log(
      `Fetching available time slots for provider ${providerId} on ${date}`
    );

    const response = await apiClient.get(
      `service/api/v1/booking/availability/${providerId}`,
      {
        params: { date },
      }
    );

    console.log('Available time slots fetched successfully:', response.data);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching available time slots:`, error);
    logger.error(`Error fetching available time slots:`, error);

    // Fallback: return mock data if API fails
    console.log('Using fallback time slot data');
    return generateFallbackTimeSlots(date);
  }
};

/**
 * Generate fallback time slots when API is unavailable
 */
const generateFallbackTimeSlots = (date: string) => {
  const timeSlots = [];
  const currentDate = new Date();
  const selectedDate = new Date(date);

  // Generate time slots from 9 AM to 5 PM with 30-minute intervals
  for (let hour = 9; hour <= 17; hour++) {
    for (const minute of [0, 30]) {
      // Skip if we're at 5:30 PM
      if (hour === 17 && minute === 30) continue;

      const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
      const period = hour < 12 ? 'AM' : 'PM';
      const startTime = `${formattedHour}:${minute === 0 ? '00' : minute} ${period}`;

      // Calculate end time (30 minutes later)
      const endHour = minute === 30 ? hour + 1 : hour;
      const endMinute = minute === 30 ? 0 : 30;
      const formattedEndHour = endHour % 12 === 0 ? 12 : endHour % 12;
      const endPeriod = endHour < 12 ? 'AM' : 'PM';
      const endTime = `${formattedEndHour}:${endMinute === 0 ? '00' : endMinute} ${endPeriod}`;

      const timeString = `${startTime}-${endTime}`;

      // Determine availability
      let available = true;
      let reason = '';
      let popularity: 'low' | 'medium' | 'high' = 'medium';

      // If the selected date is today, only show future time slots
      if (
        selectedDate.toDateString() === currentDate.toDateString() &&
        (hour < currentDate.getHours() ||
          (hour === currentDate.getHours() &&
            minute <= currentDate.getMinutes()))
      ) {
        available = false;
        reason = 'Past time';
      }

      // Simulate some random unavailability
      if (available && Math.random() > 0.7) {
        available = false;
        reason = 'Fully booked';
      }

      // Set popularity
      if (hour >= 10 && hour <= 14) {
        popularity = 'high';
      } else if (hour === 9 || hour >= 16) {
        popularity = 'low';
      }

      timeSlots.push({
        time: timeString,
        available,
        reason: available ? '' : reason,
        popularity: available ? popularity : undefined,
      });
    }
  }

  return {
    date,
    timeSlots,
  };
};

export const getStaffById = async (id: string) => {
  try {
    const res = await apiClient.get(
      `${import.meta.env.VITE_APP_BACKEND_SERVICE}/${Path.getStaff}` + `/${id}`
    );
    return res.data;
  } catch (error: unknown) {
    console.log('ERROR: ', error);
    // logger.error(error);
    throw error;
  }
};

export const getTimeSlotRelatedStaff = async (data: any) => {
  try {
    const res = await apiClient.post(
      `${import.meta.env.VITE_APP_BACKEND_SERVICE}/${Path.checkTimeslotAvailable}`,
      data
    );
    return res.data;
  } catch (error: unknown) {
    console.log('Error get staff booking  time slot : ', error);
    throw error;
  }
};

export const bookingTotalCalculation = async (data: any) => {
  try {
    const res = await apiClient.post(
      `${import.meta.env.VITE_APP_BACKEND_SERVICE}/${Path.totalCalculation}`,
      data
    );
    return res.data;
  } catch (error: unknown) {
    console.log('Error booking total calculation  : ', error);
    throw error;
  }
};

export const verifyPromoCode = async (data: PromoCode) => {
  try {
    const res = await apiClient.post(
      `${import.meta.env.VITE_APP_BACKEND_SERVICE}/${Path.verifyPromoCode}`,
      data
    );
    return res.data;
  } catch (error: unknown) {
    console.log('Error verify promo code  : ', error);
    throw error;
  }
};

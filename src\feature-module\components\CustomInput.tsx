import { Input, InputProps } from '@heroui/react';
import { ReactNode } from 'react';

interface CustomInputProps extends InputProps {
  id?: string;
  label?: string;
  name?: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  placeholder?: string;
  size?: 'sm' | 'md' | 'lg';
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'success'
    | 'warning'
    | 'danger';
  variant?: 'flat' | 'bordered' | 'underlined' | 'faded';
  labelPlacement?: 'inside' | 'outside' | 'outside-left';
  errorMessage?: string;
  description?: string;
  defaultValue?: string;
  isReadOnly?: boolean;
  isDisabled?: boolean;
  isRequired?: boolean;
  isInvalid?: boolean;
  endContent?: ReactNode;
  startContent?: ReactNode;
  onValueChange?: (value: string) => void;
  value?: string;
  className?: string;
}

const CustomInput = ({
  id,
  label = '',
  placeholder = 'Enter placeholder',
  size = 'sm',
  radius = 'sm',
  type = 'text',
  color = 'default',
  variant = 'bordered',
  labelPlacement = 'outside',
  description = '',
  errorMessage = '',
  isDisabled = false,
  isInvalid = false,
  defaultValue = '',
  name = '',
  startContent,
  endContent,
  onValueChange,
  value,
  className,

  ...props
}: CustomInputProps) => {
  return (
    <Input
      id={id}
      label={label}
      type={type}
      name={name}
      placeholder={placeholder}
      size={size}
      radius={radius}
      color={color}
      variant={variant}
      defaultValue={defaultValue}
      labelPlacement={labelPlacement}
      isDisabled={isDisabled}
      description={description}
      errorMessage={errorMessage}
      isInvalid={isInvalid}
      value={value}
      onValueChange={onValueChange}
      startContent={startContent}
      endContent={endContent}
      className={className}
      minLength={5}
      maxLength={150}
      classNames={{
        input: 'placeholder:text-gray-400 placeholder:text-xs text-xs',
        label: 'text-xs text-gray-600 error:text-blue-500 error:text-xs', // small label
        description: 'text-[11px]',
        inputWrapper: 'border border-gray-300 text-xs',
        errorMessage: 'text-[11px] text-red-500 font-thin', // small error message
        helperWrapper: 'text-xs text-gray-500',
      }}
      {...props}
    />
  );
};

export default CustomInput;

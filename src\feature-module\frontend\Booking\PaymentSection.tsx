import { useEffect, useState } from 'react';

type PackageSectionProps = {
  onChange: (type: string) => void;
};

const PaymentSection = ({ onChange }: PackageSectionProps) => {
  const [selectedPackage, setSelectedPackage] = useState<string>('');

  // Notify parent
  useEffect(() => {
    if (selectedPackage) {
      onChange(selectedPackage);
    }
  }, [selectedPackage, onChange]);

  return (
    <div className="mt-3 border p-3 rounded-md border-primary/30 bg-gray-50">
      <div className="border-b mb-2 flex justify-between items-center">
        <p className="text-title-bold  mb-2">Choose Payment Method</p>
      </div>

      <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-3">
        {/* Card */}
        <button
          onClick={() => setSelectedPackage('cash')}
          className={`flex flex-row items-center border p-1.5 rounded-md ${
            selectedPackage === 'cash'
              ? 'border-primary bg-secondary/10 '
              : 'hover-primary-gradient border-primary/30 bg-white'
          }`}
        >
          <span className="w-10 h-10 rounded-md overflow-hidden">
            <img
              src="https://readymadeui.com/images/visa.webp"
              className="w-10 h-10 object-cover"
              alt="card1"
            />
          </span>
          <span className="w-10 h-10 rounded-md overflow-hidden ml-3">
            <img
              src="https://readymadeui.com/images/american-express.webp"
              className="w-10 h-10 object-cover"
              alt="card2"
            />
          </span>

          <div className="flex flex-col text-left ml-3">
            <p className="text-body">Card Payment</p>
          </div>
        </button>

        {/* Card */}
        <button
          onClick={() => setSelectedPackage('cash')}
          className={`flex flex-row items-center border p-1.5 rounded-md ${
            selectedPackage === 'cash'
              ? 'border-primary bg-secondary/10 '
              : 'hover-primary-gradient border-primary/30 bg-white'
          }`}
        >
          <span className="w-10 h-10 rounded-md overflow-hidden">
            <img
              src="https://readymadeui.com/images/visa.webp"
              className="w-10 h-10 object-cover"
              alt="card1"
            />
          </span>
          <span className="w-10 h-10 rounded-md overflow-hidden ml-3">
            <img
              src="https://readymadeui.com/images/american-express.webp"
              className="w-10 h-10 object-cover"
              alt="card2"
            />
          </span>

          <div className="flex flex-col text-left ml-3">
            <p className="text-body">Card Payment</p>
          </div>
        </button>

        {/* Card */}
        <button
          onClick={() => setSelectedPackage('cash')}
          className={`flex flex-row items-center border p-1.5 rounded-md ${
            selectedPackage === 'cash'
              ? 'border-primary bg-secondary/10 '
              : 'hover-primary-gradient border-primary/30 bg-white'
          }`}
        >
          <span className="w-10 h-10 rounded-md overflow-hidden">
            <img
              src="https://readymadeui.com/images/visa.webp"
              className="w-10 h-10 object-cover"
              alt="card1"
            />
          </span>
          <span className="w-10 h-10 rounded-md overflow-hidden ml-3">
            <img
              src="https://readymadeui.com/images/american-express.webp"
              className="w-10 h-10 object-cover"
              alt="card2"
            />
          </span>

          <div className="flex flex-col text-left ml-3">
            <p className="text-body">Card Payment</p>
          </div>
        </button>
      </div>
    </div>
  );
};

export default PaymentSection;

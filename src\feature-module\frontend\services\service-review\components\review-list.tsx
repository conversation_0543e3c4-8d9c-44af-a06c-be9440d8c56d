"use client"

import { ReviewCard } from "./review-card"
import type { Review, Reply } from "../service-review"

interface ReviewListProps {
  reviews: Review[]
  onReply: (reviewId: string, reply: Omit<Reply, "id">) => void
  onLike?: (reviewId: string, type: 'like' | 'dislike') => void
  onReplyLike?: (reviewId: string, replyId: string, type: 'like' | 'dislike') => void
}

export function ReviewList({ reviews, onReply, onLike, onReplyLike }: ReviewListProps) {
  if (reviews.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No reviews yet. Be the first to share your experience!</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {reviews.map((review) => (
        <ReviewCard
          key={review.id}
          review={review}
          onReply={onReply}
          onLike={onLike}
          onReplyLike={onReplyLike}
        />
      ))}
    </div>
  )
}

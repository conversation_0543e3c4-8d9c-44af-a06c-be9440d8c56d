export const formatAccounting = (
  value: number | null | undefined,
  currency: string = 'USD'
): string => {
  const num = value ?? 0;

  // Format absolute value
  const formatted = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  }).format(Math.abs(num));

  // Accounting style → negatives in parentheses
  if (num < 0) {
    return `(${formatted})`;
  }

  // Add + sign for positives
  return ` ${formatted}`;
};

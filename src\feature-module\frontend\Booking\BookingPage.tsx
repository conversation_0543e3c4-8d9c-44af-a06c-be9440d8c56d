import { useLocation } from 'react-router-dom';
import { Form, FormProvider, useForm } from 'react-hook-form';
import DateAndStaff from './DateAndStaff';
import { useFetchServiceDataById } from '../../../hook/useQueryData';
import { useEffect, useState, useMemo } from 'react';
import AdditionalDetails from './AdditionalDetails';
import AddressSection from './AddressSection';
import {
  AdditionalGet,
  PackageGet,
  PersonalGet,
  ServiceGet,
} from '../../../utils/type';
import CustomChip from '../../components/CustomChip';
import { TbRosetteDiscountCheckFilled } from 'react-icons/tb';
import { LuCalendarCheck, LuClock3, LuUser } from 'react-icons/lu';
import moment from 'moment';
import { yupResolver } from '@hookform/resolvers/yup';
import { personalInfoSchema } from '../../../utils/validation/personalInfoSchema';
import { AiOutlineMail } from 'react-icons/ai';
import { SlLocationPin } from 'react-icons/sl';
import { Divider } from '@heroui/react';
import CustomInput from '../../components/CustomInput';
import CustomButton from '../../components/CustomButton';
import { RiCheckboxCircleFill } from 'react-icons/ri';
import { IoMdCloseCircle } from 'react-icons/io';
import {
  bookingTotalCalculation,
  verifyPromoCode,
} from '../../../service/bookingService';
import { BiSolidDiscount } from 'react-icons/bi';
import PackageSection from './PackageSection';
import PaymentSection from './PaymentSection';
import { formatAccounting } from '../../../utils/numberFormat';

export interface IAdditionalService {
  id: string;
  serviceItem: string;
  price: number;
  images: string;
}

interface ITimeAndStaff {
  date: string;
  timeSlotId: string;
  time: string;
  staffId: string;
  staff: StaffMember;
}

type StaffMember = {
  staffId: string;
  fullName: string;
  providerStaffId: string;
  isProvider: boolean;
};

type CalculationGet = {
  discount: number;
  discountId: string;
  serviceId: string;
  subtotal: number;
  tax: number;
  total: number;
};

const BookingPage = () => {
  const location = useLocation();
  const [packages, setPackage] = useState<PackageGet>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isPromoLoading, setIsPromoloading] = useState<boolean>(false);
  const [calculation, setCalculation] = useState<CalculationGet>();
  const [timeAndStaff, setTimeAndStaff] = useState<ITimeAndStaff>();
  const [additonalServiceIds, setAdditonalServiceIds] = useState<string[]>([]);
  const [packageId, setPackageId] = useState<string>('');
  const [promoCode, setPromoCode] = useState<string>('');
  const [promoCodeError, setPromoCodeError] = useState<string>('');
  const [promoCodeResponse, setPromoCodeResponse] = useState({
    discountId: '',
    promoCode: '',
  });
  const [paymentType, setPaymentType] = useState<string>('');
  const [additonalService, setAdditonalService] = useState<
    IAdditionalService[]
  >([]);

  const { serviceId } = location.state || {};

  const { data } = useFetchServiceDataById(serviceId);
  const service: ServiceGet = useMemo(() => data?.serviceInfo || [], [data]);

  console.log('Service: ', service);
  console.log('paymentType: ', paymentType);

  const form = useForm<PersonalGet>({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: {
        street: '',
        city: '',
        state: '',
        postalCode: '',
      },
      bookingNotes: undefined,
    },
    shouldUnregister: true,
    mode: 'onChange',
    resolver: yupResolver(personalInfoSchema) as any,
    reValidateMode: 'onChange',
  });

  const { watch } = form;

  console.log('Watch: ', watch());
  const email = watch('email');
  const firstName = watch('firstName');
  const lastName = watch('lastName');
  const address = watch('address');

  const services = useMemo(() => {
    if (!data?.serviceInfo) return [];
    return Array.isArray(data.serviceInfo)
      ? data.serviceInfo
      : [data.serviceInfo];
  }, [data]);

  useEffect(() => {
    if (!services?.length || !packageId) return;

    let matchedPackage: any = null;

    services.forEach((srv: ServiceGet) => {
      const pkg = srv?.packages?.find(
        (p: PackageGet) => p?.packageId === packageId
      );
      if (pkg) matchedPackage = pkg;
    });

    setPackage(matchedPackage);
  }, [packageId, services]);

  useEffect(() => {
    const matchedAdditionalService = services?.map((srv: ServiceGet) => {
      const matched = srv?.additionalServices?.filter((as: AdditionalGet) =>
        additonalServiceIds.includes(as?.id)
      );
      return matched;
    });
    setAdditonalService(matchedAdditionalService[0] || []);
  }, [additonalServiceIds, services]);

  const onSubmit = async (data: any) => {
    console.log('SumbitData', data);
  };

  useEffect(() => {
    const calculateTotal = async () => {
      setIsLoading(true);
      try {
        if (!serviceId) return;

        let isPromoCode = false;

        if (promoCodeResponse?.discountId && promoCodeResponse?.promoCode) {
          isPromoCode = true;
        }
        console.log('PAYLOAD: ', isPromoCode);

        const discountId = isPromoCode
          ? promoCodeResponse?.discountId
          : packages?.isDiscount
            ? packages?.discount?.discountId
            : '';

        const finalPayload = {
          serviceId,
          packageId: packages?.packageId || '',
          additionalServiceIds: additonalServiceIds,
          discountId: discountId,
          promoCode: isPromoCode ? promoCodeResponse?.promoCode : '',
        };

        console.log('PAYLOAD_2: ', finalPayload);

        const res = await bookingTotalCalculation(finalPayload);
        setCalculation(res?.result);
        console.log('PAYLOAD res: ', res);
      } catch (error) {
        console.error('Error calculating ', error);
      } finally {
        setIsLoading(false);
      }
    };

    calculateTotal();
  }, [additonalServiceIds, serviceId, promoCodeResponse]);

  const getDiscountLabel = () => {
    const discount = service?.isPackage
      ? packages?.discount
      : service?.discount;
    if (!discount) return '';
    return `${discount.valueType !== 'percentage' ? '$' : ''}${discount.amount}${
      discount.valueType === 'percentage' ? '%' : ''
    } Save`;
  };

  const handleVerifyPromoCode = async () => {
    try {
      setIsPromoloading(true);
      setPromoCodeError('');
      console.log('Run.....................');
      if (!promoCode) return;

      const discountId =
        service?.isDiscount && service?.discount?.discountType === 'promo-code'
          ? service?.discount?.discountId
          : '';

      const payload = {
        // discountId: 'DCID_0019',
        // promoCode: 'HOME',
        discountId: discountId,
        promoCode: promoCode,
      };
      console.log('PAYLOAD:payload', payload);
      const res = await verifyPromoCode(payload);
      console.log('PAYLOAD: ', res);

      if (res) {
        setPromoCodeError('success');
        setPromoCodeResponse(payload);
      }
    } catch (error) {
      setPromoCodeError('error');
      console.error('Error verifying promo code: ', error);
    } finally {
      setIsPromoloading(false);
    }
  };

  return (
    <div className="">
      <div>
        <p className="mt-2 text-xl font-semibold text-primary">
          Book Your Appointment
        </p>
        <p className="mt-1 text-xs text-gray-600">
          Choose your preferred date, time, and service options to schedule an
          appointment with ease. Fill in your details and confirm your booking
          in just a few simple steps.
        </p>
      </div>
      <div>
        <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-5 ">
          <div className="xl:col-span-2 ">
            {service?.isPackage && (
              <PackageSection data={service} onChange={setPackageId} />
            )}
            <DateAndStaff data={service} onChange={setTimeAndStaff} />
            {service?.additionalServices?.length > 0 && (
              <AdditionalDetails
                data={service}
                onChange={setAdditonalServiceIds}
              />
            )}
            <FormProvider {...form}>
              <Form onSubmit={onSubmit}>
                <AddressSection />
              </Form>
            </FormProvider>

            <PaymentSection onChange={setPaymentType} />
          </div>

          <div className="mt-4">
            <div className="border-primary/30 bg-gray-50  border p-3 rounded-md ">
              <div className="border-b mb-2 flex justify-between items-center">
                <p className="text-title-bold mb-2">Order Summary</p>
              </div>

              {/* FULL SERVICE */}
              <p className="text-body mb-1">Service</p>
              <div className="flex items-center border p-2 rounded-md transition border-primary/40 bg-white">
                {/* Image */}
                <div className="min:w-16 min:h-16 bg-gray-100 rounded-md overflow-hidden aspect-square">
                  <img
                    src={
                      service?.gallery?.[0]?.serviceImages?.[0] ||
                      'https://cdn.staging.gigmosaic.ca/common/fallback-image.png'
                    }
                    alt="image"
                    className="w-16 h-16 object-cover aspect-square text-xs"
                  />
                </div>

                {/* Service content */}
                <div className="ml-3 flex flex-col justify-between flex-1 ">
                  <div className="flex justify-between items-start w-full ">
                    <p className=" text-body-bold truncate line-clamp-1">
                      {service?.serviceTitle || '-'}
                    </p>
                    {(service?.isPackage
                      ? packages?.isDiscount
                      : service?.isDiscount) && (
                      <CustomChip
                        label={getDiscountLabel()}
                        color="success"
                        size="sm"
                        startContent={
                          <TbRosetteDiscountCheckFilled className="text-sm" />
                        }
                        classNames={{ base: 'text-xs', content: 'text-xs' }}
                      />
                    )}
                  </div>
                  {service?.isPackage && (
                    <p className="text-body ">
                      Package: {packages?.packageName || '-'}
                    </p>
                  )}

                  {service?.isPackage ? (
                    <p className="text-body-bold mt-1 ">
                      ${packages?.price || '-'}
                    </p>
                  ) : (
                    <p className="text-body-bold mt-1">
                      ${service?.price || '-'}
                    </p>
                  )}
                </div>
              </div>

              {/* TIME SERVICE */}
              <div className="mt-4 min-w-full">
                <p className="text-body">Appoitment Details</p>
                <div className="flex flex-row items-center border p-2.5 rounded-md transition bg-white mt-1">
                  {/* <div className="flex justify-between items-center w-full"> */}
                  <div className="grid grid-cols-2 xl:grid-cols-3 w-full items-center">
                    {/* 1 */}
                    <div className="flex flex-col gap-2">
                      <p className="text-body">Appointmant Date</p>
                      <div className="flex flex-initial gap-1">
                        <LuCalendarCheck className="text-secondary" />
                        <p className="text-body-bold">
                          {moment(timeAndStaff?.date).format('DD MMM YYYY') ||
                            '-'}
                        </p>
                      </div>
                    </div>

                    {/* 2 */}
                    <div className="flex flex-col gap-2">
                      <p className="text-body">Appointmant Time</p>
                      <div className="flex flex-initial gap-1">
                        <LuClock3 className="text-secondary" />
                        <p className="text-body-bold">
                          {timeAndStaff?.time || '-'}
                        </p>
                      </div>
                    </div>

                    {/* 3 */}
                    <div className="flex flex-col gap-2 xl:items-end xl:mt-0">
                      <div>
                        <p className="text-body">Select Staff</p>
                        <div className="flex flex-initial gap-1  mt-1.5">
                          <LuUser className="text-secondary" />
                          <p className="text-body-bold">
                            {timeAndStaff?.staff?.fullName || '-'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* end */}

              {/* ADDITONAL SERVICE */}
              <div className="mt-4 min-w-full">
                {additonalService?.length > 0 && (
                  <p className="text-body">Addtional Service</p>
                )}
                {additonalService?.map(
                  (a: IAdditionalService, index: number) => (
                    <div
                      key={index}
                      className="flex flex-row items-center border p-1.5 rounded-md transition bg-white mt-1"
                    >
                      <span className="w-10 h-10 bg-gray-100 rounded-md overflow-hidden">
                        <img
                          src={
                            a?.images ||
                            'https://cdn.staging.gigmosaic.ca/common/fallback-image.png'
                          }
                          alt="Service"
                          className="w-16 h-16 object-cover aspect-square"
                        />
                      </span>

                      <div className="flex flex-col text-left ml-3">
                        <p className="text-body">{a?.serviceItem || '-'}</p>
                        <p className="text-body-bold">${a?.price || '-'}</p>
                      </div>
                    </div>
                  )
                )}
              </div>
              {/* end */}

              {/* ADDRESS */}
              <div className="mt-4 min-w-full">
                <p className="text-body">Address Information</p>
                <div className="flex flex-row items-center border p-2.5 rounded-md transition bg-white mt-1">
                  <div className="grid grid-cols-2 xl:grid-cols-3 w-full items-center">
                    {/* Name */}
                    <div className="flex flex-col gap-2">
                      <p className="text-body">Your name</p>
                      <div className="flex flex-initial gap-1">
                        <LuUser className="text-secondary" />
                        <p className="text-body-bold">
                          {lastName && firstName
                            ? `${lastName} ${firstName}`
                            : '-'}
                        </p>
                      </div>
                    </div>

                    {/* Email */}
                    <div className="flex flex-col gap-2">
                      <p className="text-body">Email</p>
                      <div className="flex flex-initial gap-1">
                        <AiOutlineMail className="text-secondary" />
                        <p className="text-body-bold">{email || '-'}</p>
                      </div>
                    </div>

                    {/* Address */}
                    <div className="flex flex-col gap-2 xl:items-end mt-3 xl:mt-0">
                      <div>
                        <p className="text-body">Address</p>
                        <div className="flex flex-initial gap-1 mt-1">
                          <SlLocationPin className="text-secondary text-sm" />
                          <p className="text-body-bold">
                            {address?.street || ''} {address?.city || ''} <br />
                            {address?.state || ''} {address?.postalCode}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* end */}

              {/* Promo-code */}
              <div className="mt-4">
                <p className="text-body">Promo Code</p>
                <div className="flex flex-col items-center border bg-white p-2 rounded-md transition  mt-1">
                  <div className="flex gap-3 w-full">
                    <CustomInput
                      placeholder="Enter Promo Code"
                      type="text"
                      startContent={<BiSolidDiscount size={16} />}
                      onValueChange={(e) => setPromoCode(e)}
                      isDisabled={
                        isPromoLoading || promoCodeError === 'success'
                      }
                      className="capsitalize"
                    />
                    <CustomButton
                      label="Apply"
                      color="primary"
                      onPress={handleVerifyPromoCode}
                      isDisabled={
                        isPromoLoading || promoCodeError === 'success'
                      }
                      isLoading={isPromoLoading}
                    />
                  </div>

                  {promoCodeError === 'success' ? (
                    // ✅ Success
                    <div className="flex w-full mt-2">
                      <div className="flex flex-initial items-center bg-green-100 gap-1 w-full p-2 rounded-md">
                        <RiCheckboxCircleFill className="text-green-500 text-xl" />
                        <p className="text-xs text-gray-700">
                          Your promo code successfully applied!
                        </p>
                      </div>
                    </div>
                  ) : promoCodeError === 'error' ? (
                    // ❌ Error
                    <div className="flex w-full mt-2">
                      <div className="flex flex-initial items-center bg-red-100 gap-1 w-full p-2 rounded-md">
                        <IoMdCloseCircle className="text-red-500 text-xl" />
                        <p className="text-xs text-gray-700">
                          Invalid promo code. Please try again.
                        </p>
                      </div>
                    </div>
                  ) : null}
                </div>
              </div>

              <div className="mt-4 min-w-full">
                <p className="text-body">Payment Method</p>
                <button className="flex flex-row items-center border bg-white p-2 rounded-md transition  mt-1 w-full">
                  <span className="w-10 h-10 rounded-md overflow-hidden">
                    <img
                      src="https://readymadeui.com/images/visa.webp"
                      className="w-10 h-10 object-cover"
                      alt="card1"
                    />
                  </span>
                  <span className="w-10 h-10 rounded-md overflow-hidden ml-3">
                    <img
                      src="https://readymadeui.com/images/american-express.webp"
                      className="w-10 h-10 object-cover"
                      alt="card2"
                    />
                  </span>

                  <div className="flex flex-col text-left ml-3">
                    <p className="text-body">Card Payment</p>
                  </div>
                </button>
              </div>

              {/* Total */}
              <div className="mt-4 min-w-full ">
                <p className="text-body">Payment Summary</p>
                <div className="flex flex-col items-center border border-primary/30 p-2.5 rounded-md transition bg-secondary/10  mt-1">
                  <div className="grid gap-4 w-full">
                    {/* Name */}
                    {/* <div className="flex justify-between gap-2">
                      <p className="text-body">
                        Service{' '}
                        <span>
                          {service?.isPackage &&
                            ` : (${packages?.packageName})`}
                        </span>
                      </p>

                      {isLoading ? (
                        <p className="text-[10px] text-gray-400">
                          Calculating...
                        </p>
                      ) : (
                        <>
                          {service?.isPackage ? (
                            <p className="text-body-bold mt-1 ">
                              {formatAccounting(packages?.price || 0)}
                            </p>
                          ) : (
                            <p className="text-body-bold mt-1">
                              {formatAccounting(service?.price || 0)}
                            </p>
                          )}
                        </>
                      )}
                    </div> */}

                    <div className="flex justify-between gap-2">
                      <p className="text-body">Subtotal</p>
                      {isLoading ? (
                        <p className="text-[10px] text-gray-400">
                          Calculating...
                        </p>
                      ) : (
                        <p className="text-body-bold">
                          {formatAccounting(calculation?.subtotal || 0)}
                        </p>
                      )}
                    </div>

                    {/* Address */}
                    <div className="flex justify-between gap-2">
                      <p className="text-xs text-green-600">Discount</p>
                      {/* <p className="text-body">{service}</p> */}
                      {isLoading ? (
                        <p className="text-[10px] text-gray-400">
                          Calculating...
                        </p>
                      ) : (
                        <p className="text-xs font-semibold text-green-600">
                          - {formatAccounting(calculation?.discount || 0)}
                        </p>
                      )}
                    </div>

                    {/* Email */}
                    <div className="flex justify-between gap-2">
                      <p className="text-danger ">Tax</p>
                      {isLoading ? (
                        <p className="text-[10px] text-gray-400">
                          Calculating...
                        </p>
                      ) : (
                        <p className="text-danger-bold">
                          + {formatAccounting(calculation?.tax)}
                        </p>
                      )}
                    </div>

                    <Divider />

                    <div className="flex justify-between gap-2">
                      <p className="text-body-bold">Total</p>
                      {isLoading ? (
                        <p className="text-[10px] text-gray-400">
                          Calculating...
                        </p>
                      ) : (
                        <p className="text-body-bold">
                          {formatAccounting(calculation?.total)}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              {/* end */}
              <div className="w-full mt-5">
                <CustomButton
                  label="Confirm Booking"
                  className="w-full"
                  color="primary"
                  size="md"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingPage;

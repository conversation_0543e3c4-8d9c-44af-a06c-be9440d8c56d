/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Circle,
  useMapEvents,
} from 'react-leaflet';
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Select,
  SelectItem,
} from '@heroui/react';
import { BiMap, BiCurrentLocation, BiChevronDown } from 'react-icons/bi';
import 'leaflet/dist/leaflet.css';
import CustomInput from './CustomInput';

interface LocationFilterProps {
  latitude: number;
  longitude: number;
  radius: number;
  onChange: (lat: number, lng: number, radius: number) => void;
  address?: string;
}

const LocationFilter: React.FC<LocationFilterProps> = ({
  latitude,
  longitude,
  radius,
  onChange,
  address: initialAddress,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [address, setAddress] = useState(initialAddress || '');
  const [tempRadius, setTempRadius] = useState(radius);
  const [tempLat, setTempLat] = useState(latitude);
  const [tempLng, setTempLng] = useState(longitude);

  // Radius options for dropdown
  const radiusOptions = [
    { value: 1, label: '1 km' },
    { value: 5, label: '5 km' },
    { value: 10, label: '10 km' },
    { value: 25, label: '25 km' },
    { value: 50, label: '50 km' },
    { value: 100, label: '100 km' },
    { value: 200, label: '200 km' },
    { value: 250, label: '250 km' },
  ];

  useEffect(() => {
  if (initialAddress) {
    setAddress(initialAddress);
  }
}, [initialAddress]);

  // Handle map click & drag
  const LocationMarker = () => {
    useMapEvents({
      click: (e) => {
        const { lat, lng } = e.latlng;
        setTempLat(lat);
        setTempLng(lng);
        reverseGeocode(lat, lng);
      },
    });

    return (
      <>
        <Marker
          draggable
          position={[tempLat, tempLng]}
          eventHandlers={{
            dragend: (e) => {
              const { lat, lng } = e.target.getLatLng();
              setTempLat(lat);
              setTempLng(lng);
              reverseGeocode(lat, lng);
            },
          }}
        />
        <Circle center={[tempLat, tempLng]} radius={tempRadius * 1000} />
      </>
    );
  };

  // Reverse geocode to get address from coordinates
  const reverseGeocode = async (lat: number, lng: number) => {
    try {
      const response = await fetch(
        `https://api.geoapify.com/v1/geocode/reverse?lat=${lat}&lon=${lng}&apiKey=${import.meta.env.VITE_APP_GEOPIFY_APIKEY}`
      );
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        setAddress(data.features[0].properties.formatted);
        setSearchText(data.features[0].properties.formatted);
      }
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
    }
  };

  // Geoapify autocomplete
  useEffect(() => {
    if (searchText.length < 3) {
      setSuggestions([]);
      return;
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      fetch(
        `https://api.geoapify.com/v1/geocode/autocomplete?text=${encodeURIComponent(
          searchText
        )}&countrycodes=ca&apiKey=${import.meta.env.VITE_APP_GEOPIFY_APIKEY}`,
        { signal: controller.signal }
      )
        .then((res) => res.json())
        .then((data) => setSuggestions(data.features || []))
        .catch(() => setSuggestions([]));
    }, 300);

    return () => {
      controller.abort();
      clearTimeout(timeoutId);
    };
  }, [searchText]);

  const handleSelectSuggestion = (feature: any) => {
    const [lng, lat] = feature.geometry.coordinates;
    setTempLat(lat);
    setTempLng(lng);
    setAddress(feature.properties.formatted);
    setSearchText(feature.properties.formatted);
    setSuggestions([]);
  };

  const useCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (pos) => {
          setTempLat(pos.coords.latitude);
          setTempLng(pos.coords.longitude);
          reverseGeocode(pos.coords.latitude, pos.coords.longitude);
        },
        (error) => {
          console.error('Geolocation error:', error);
        }
      );
    }
  };

  const applyLocation = () => {
    onChange(tempLat, tempLng, tempRadius);
    setIsModalOpen(false);
  };

  const openModal = () => {
    setTempLat(latitude);
    setTempLng(longitude);
    setTempRadius(radius);
    setSearchText(address);
    setIsModalOpen(true);
  };

  const handleRadiusChange = (value: number) => {
    setTempRadius(value);
  };

  return (
    <>
      {/* Location display button - shows truncated address */}
      <div
        className="flex items-center justify-between p-2 border rounded-md cursor-pointer hover:bg-gray-50"
        onClick={openModal}
      >
        <div className="flex items-center">
            <div>
                 <BiMap className="mr-2" />
            </div>
         
          <span className='line-clamp-1  text-ellipsis'>{address}</span>
        </div>
        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
          {radius} km
        </span>
      </div>

      {/* Location selection modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            Set Location
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              {/* Address search */}

              {/* Radius selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <CustomInput
                    type="text"
                    label="Address, city or postal code"
                    value={searchText}
                    onValueChange={(value) => setSearchText(value)}
                    placeholder="Address, city or postal code"
                    className=""
                  />
                  {suggestions.length > 0 && (
                    <ul className="absolute z-[9999] w-full mt-1 bg-white border border-gray-300 rounded-sm shadow-lg">
                      {suggestions.map((s, i) => (
                        <li
                          key={i}
                          onClick={() => handleSelectSuggestion(s)}
                          className="cursor-pointer hover:bg-gray-100 p-2"
                        >
                          {s.properties.formatted}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
                {/* Radius dropdown */}
                <div>
                  <Select
                    label="Select radius"
                    aria-label="Select radius"
                    labelPlacement="outside"
                    className="w-full bg-white"
                    radius="none"
                    selectedKeys={[tempRadius.toString()]}
                    onChange={(e) => handleRadiusChange(Number(e.target.value))}
                    selectorIcon={<BiChevronDown />}
                  >
                    {radiusOptions.map((option) => (
                      <SelectItem
                        key={option.value.toString()}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              </div>

              {/* Use current location button */}
              <button
                onClick={useCurrentLocation}
                className="flex items-center text-sm text-blue-600"
              >
                <BiCurrentLocation className="mr-1" />
                Use current location
              </button>

              {/* Map */}
              <div className="h-64 rounded-md overflow-hidden">
                <MapContainer
                key={`${tempLat}-${tempLng}`}
                  center={[tempLat, tempLng]}
                  zoom={10}
                  style={{ height: '100%', width: '100%' }}
                >
                  <TileLayer
                    attribution="&copy; OpenStreetMap contributors"
                    url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  />
                  <LocationMarker />
                </MapContainer>
              </div>

            </div>
          </ModalBody>
          <ModalFooter>
            <button
              onClick={() => setIsModalOpen(false)}
              className="px-4 py-2 border rounded-md mr-2"
            >
              cancel
            </button>
            <button
              onClick={applyLocation}
              className="px-4 py-2 bg-blue-600 text-white rounded-md"
            >
              apply
            </button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
};

export default LocationFilter;

const { heroui } = require('@heroui/react');

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
    './node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        primary: ['Roboto', 'sans-serif'],
      },
      colors: {
        // primary: "#12BBB5",
        // secondary: "#7965C1",
        // background: "#000000",
        // graycolor: "#141414",
        // white: "#ECECEC",
        // textSecondary: "#595959",
        // darkModeBackground: "#121212",
        // darkModeBackgroundSecondary: "#18181B",
        // sideBarBackground: "#252E3E",
        // darkModeText: "#FFFFFF",
        // darkModeTextSecondary: "#B0B0B0",
        // zinc: {
        //   900: "#06B7DB",
        // },

        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          hover: 'hsl(var(--primary-hover))',
          light: 'hsl(var(--primary-light))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))',
          light: 'hsl(var(--success-light))',
        },
        warning: {
          DEFAULT: 'hsl(var(--warning))',
          foreground: 'hsl(var(--warning-foreground))',
          light: 'hsl(var(--warning-light))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },

      backgroundImage: {
        'primary-gradient':
          'linear-gradient(90deg, rgba(18, 187, 181, 1) 28%, rgba(109, 111, 192, 1) 68%)',
      },

      screens: {
        tablet: '431px', //Iphone 15 pro max
        laptop: '1024px', // 1024px
        desktop: '1280px', // 1280px
      },
    },
  },
  darkMode: 'class',
  // plugins: [
  //   heroui({
  //     prefix: "nextui",
  //     addCommonColors: true,
  //     themes: {
  //       dark: {
  //         color: {
  //           primary: {,
  //             background: "#FF7A30",
  //           },
  //         },
  //       },
  //     },
  //   }),
  // ],
  plugins: [
    heroui({
      prefix: 'nextui',
      addCommonColors: true,
      themes: {
        dark: {
          colors: {
            // background: "#006FEE",
            // content1: "#2F3349",
          },
        },
      },
    }),
  ],
};

import { useEffect, useMemo, useState } from 'react';
import CustomChip from '../../components/CustomChip';

import { ServiceGet } from '../../../utils/type';
import ProviderOrverview from './ProviderOrverview';
import ProviderReview from './ProviderReview';
import TabNav from '../../components/common/ui/TabNav';
import { useParams } from 'react-router-dom';
import {
  useFetchProviderById,
  useFetchServiceDataByProvider,
} from '../../../hook/useQueryData';
import { Pagination } from '@heroui/react';

const ProviderProfile = () => {
  const { id } = useParams();
  const [activeTab, setActiveTab] = useState('overview');
  const [service, setService] = useState<ServiceGet[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const { data, isFetching: isFetchingProvider } = useFetchProviderById(id);
  const { data: providerPaylaod, isFetching: isFetchingService } =
    useFetchServiceDataByProvider(currentPage, 15, 'G_UID_73');

  console.log('Privider: ', data);
  console.log('providerPaylaod: ', providerPaylaod);
  const totalPage = useMemo(
    () => providerPaylaod?.pages || 1,
    [providerPaylaod]
  );

  useEffect(() => {
    setService(providerPaylaod?.services);
  }, [currentPage]);

  console.log('Data: Data: ', service);

  const tabs = [
    { id: 'overview', title: 'Overview' },
    { id: 'reviews', title: 'Reviews & Ratings' },
  ];

  return (
    <div>
      {/* <div className="bg-[linear-gradient(90deg,hsla(196,100%,95%,1)_0%,hsla(0,0%,100%,1)_100%)] h-[200px] flex justify-end items-center rounded-xl shadow-l-sm mt-3"></div> */}

      {/* Profile */}
      <div className="flex mt-5 justify-between">
        <div className="flex ml-5 ">
          <img
            src={
              data?.user?.profilePicture ||
              'https://cdn.staging.gigmosaic.ca/common/fallback-image.png'
            }
            alt="Profile"
            className="rounded-full size-36 border-5 border-white object-cover"
          />

          {/* Profile name and other */}
          <div className="flex flex-col items-start mt-14 ml-4">
            <h1 className="text-2xl font-semibold text-gray-800">
              Auto Car Repaire
            </h1>

            <div className="mt-2">
              <CustomChip
                label={
                  data?.user?.ISVerified ? 'Verified Provider' : 'Not Verified'
                }
                color={data?.user?.ISVerified ? 'primary' : 'warning'}
                size="sm"
                variant="flat"
                className="text-xs font-semibold"
              />
            </div>
          </div>
        </div>

        {/* Button */}
        <div className="flex mt-16 ">
          {/* <CustomButton
            label="Follow"
            color="primary"
            radius="sm"
            variant="flat"
          /> */}
        </div>
      </div>

      {/* Tab Navigation */}
      <TabNav activeTab={activeTab} onTabChange={setActiveTab} tabs={tabs} />

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <>
          <ProviderOrverview
            providerData={data?.user}
            service={providerPaylaod?.services || []}
            isProviderLoading={isFetchingProvider}
            isServiceLoading={isFetchingService}
          />
          <div className="flex justify-end items-end">
            <Pagination
              className="pl-5 mt-10"
              showControls
              size="sm"
              page={currentPage}
              total={totalPage}
              onChange={setCurrentPage}
            />
          </div>
        </>
      )}

      {activeTab === 'reviews' && <ProviderReview />}
    </div>
  );
};

export default ProviderProfile;

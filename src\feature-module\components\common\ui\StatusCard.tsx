import { Card, CardBody } from '@heroui/react';
import { ReactNode } from 'react';

interface ReusableCardProps {
  title?: string;
  icon?: ReactNode;
  value?: string | number;
  shadow?: 'none' | 'sm' | 'md' | 'lg';
  isHoverEffect?: boolean;
  className?: string;
}

const StatusCard = ({
  title,
  icon,
  value,
  shadow,
  isHoverEffect,
  className = '',
}: ReusableCardProps) => {
  return (
    <Card
      shadow="none"
      className={`border-1 border-primary  border-b-3
          ${shadow !== 'none' ? `shadow-${shadow}` : ''} 
    ${isHoverEffect ? 'hover:scale-105 transition-transform duration-200' : ''} 
    ${className}`}
    >
      <CardBody className="flex justify-center items-center">
        <div className="flex flex-col items-center justify-center">
          {icon}
          <p className="text-xl font-semibold text-gray-700 ">{value}</p>
          <p className="text-sm text-gray-600 ">{title}</p>
        </div>
      </CardBody>
    </Card>
  );
};

export default StatusCard;
